/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analytics/errors/route";
exports.ids = ["app/api/analytics/errors/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Ferrors%2Froute&page=%2Fapi%2Fanalytics%2Ferrors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Ferrors%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Ferrors%2Froute&page=%2Fapi%2Fanalytics%2Ferrors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Ferrors%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_analytics_errors_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analytics/errors/route.ts */ \"(rsc)/./src/app/api/analytics/errors/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analytics/errors/route\",\n        pathname: \"/api/analytics/errors\",\n        filename: \"route\",\n        bundlePath: \"app/api/analytics/errors/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\analytics\\\\errors\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_analytics_errors_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Ferrors%2Froute&page=%2Fapi%2Fanalytics%2Ferrors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Ferrors%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/analytics/errors/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/analytics/errors/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n        // Get authenticated user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const days = parseInt(searchParams.get('days') || '30');\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - days);\n        // Get total requests for error rate calculation\n        const { data: totalRequestsData, count: totalRequests } = await supabase.from('request_logs').select('id', {\n            count: 'exact'\n        }).eq('user_id', user.id).gte('request_timestamp', startDate.toISOString());\n        // Get failed requests (status codes >= 400)\n        const { data: failedRequestsData, count: failedRequests } = await supabase.from('request_logs').select('id, status_code, error_message, error_source, request_timestamp', {\n            count: 'exact'\n        }).eq('user_id', user.id).gte('request_timestamp', startDate.toISOString()).gte('status_code', 400);\n        // Calculate error rate\n        const errorRate = totalRequests > 0 ? failedRequests / totalRequests * 100 : 0;\n        // Get error breakdown by status code\n        const statusCodeBreakdown = {};\n        const errorSourceBreakdown = {};\n        const errorMessages = [];\n        if (failedRequestsData) {\n            failedRequestsData.forEach((log)=>{\n                // Status code breakdown\n                const statusCode = log.status_code;\n                statusCodeBreakdown[statusCode] = (statusCodeBreakdown[statusCode] || 0) + 1;\n                // Error source breakdown\n                const errorSource = log.error_source || 'unknown';\n                errorSourceBreakdown[errorSource] = (errorSourceBreakdown[errorSource] || 0) + 1;\n                // Collect error messages\n                if (log.error_message) {\n                    errorMessages.push({\n                        message: log.error_message,\n                        source: log.error_source,\n                        statusCode: log.status_code,\n                        timestamp: log.request_timestamp\n                    });\n                }\n            });\n        }\n        // Get top error status codes\n        const topStatusCodes = Object.entries(statusCodeBreakdown).sort(([, a], [, b])=>b - a).slice(0, 10).map(([code, count])=>({\n                statusCode: parseInt(code),\n                count,\n                description: getStatusCodeDescription(parseInt(code))\n            }));\n        // Get top error sources\n        const topErrorSources = Object.entries(errorSourceBreakdown).sort(([, a], [, b])=>b - a).slice(0, 10).map(([source, count])=>({\n                source,\n                count\n            }));\n        // Get error trends over time (daily error counts)\n        const dailyErrors = {};\n        if (failedRequestsData) {\n            failedRequestsData.forEach((log)=>{\n                const date = new Date(log.request_timestamp).toISOString().split('T')[0];\n                dailyErrors[date] = (dailyErrors[date] || 0) + 1;\n            });\n        }\n        // Convert to time series format\n        const errorTimeSeriesData = [];\n        for(let i = days - 1; i >= 0; i--){\n            const date = new Date();\n            date.setDate(date.getDate() - i);\n            const dateStr = date.toISOString().split('T')[0];\n            errorTimeSeriesData.push({\n                date: dateStr,\n                errors: dailyErrors[dateStr] || 0\n            });\n        }\n        // Get recent error messages (last 20)\n        const recentErrors = errorMessages.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 20);\n        // Calculate error rate trend (compare with previous period)\n        const previousStartDate = new Date(startDate);\n        previousStartDate.setDate(previousStartDate.getDate() - days);\n        const { count: previousTotalRequests } = await supabase.from('request_logs').select('id', {\n            count: 'exact'\n        }).eq('user_id', user.id).gte('request_timestamp', previousStartDate.toISOString()).lt('request_timestamp', startDate.toISOString());\n        const { count: previousFailedRequests } = await supabase.from('request_logs').select('id', {\n            count: 'exact'\n        }).eq('user_id', user.id).gte('request_timestamp', previousStartDate.toISOString()).lt('request_timestamp', startDate.toISOString()).gte('status_code', 400);\n        const previousErrorRate = previousTotalRequests > 0 ? previousFailedRequests / previousTotalRequests * 100 : 0;\n        const errorRateTrend = errorRate - previousErrorRate;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                // Summary metrics\n                totalRequests: totalRequests || 0,\n                failedRequests: failedRequests || 0,\n                errorRate,\n                errorRateTrend,\n                // Breakdowns\n                statusCodeBreakdown: topStatusCodes,\n                errorSourceBreakdown: topErrorSources,\n                // Time series data\n                errorTimeSeriesData,\n                // Recent errors\n                recentErrors,\n                // Period info\n                period: `${days} days`,\n                startDate: startDate.toISOString(),\n                endDate: new Date().toISOString()\n            }\n        });\n    } catch (error) {\n        console.error('Error in errors analytics:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch errors analytics',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nfunction getStatusCodeDescription(statusCode) {\n    const descriptions = {\n        400: 'Bad Request',\n        401: 'Unauthorized',\n        403: 'Forbidden',\n        404: 'Not Found',\n        405: 'Method Not Allowed',\n        409: 'Conflict',\n        422: 'Unprocessable Entity',\n        429: 'Too Many Requests',\n        500: 'Internal Server Error',\n        502: 'Bad Gateway',\n        503: 'Service Unavailable',\n        504: 'Gateway Timeout'\n    };\n    return descriptions[statusCode] || 'Unknown Error';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hbmFseXRpY3MvZXJyb3JzL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF3RDtBQUNzQjtBQUV2RSxlQUFlRSxJQUFJQyxPQUFvQjtJQUM1QyxJQUFJO1FBQ0YsTUFBTUMsV0FBV0gsMkZBQXFDQSxDQUFDRTtRQUV2RCx5QkFBeUI7UUFDekIsTUFBTSxFQUFFRSxNQUFNLEVBQUVDLElBQUksRUFBRSxFQUFFQyxPQUFPQyxTQUFTLEVBQUUsR0FBRyxNQUFNSixTQUFTSyxJQUFJLENBQUNDLE9BQU87UUFDeEUsSUFBSUYsYUFBYSxDQUFDRixNQUFNO1lBQ3RCLE9BQU9OLHFEQUFZQSxDQUFDVyxJQUFJLENBQUM7Z0JBQUVKLE9BQU87WUFBZSxHQUFHO2dCQUFFSyxRQUFRO1lBQUk7UUFDcEU7UUFFQSxNQUFNLEVBQUVDLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUlYLFFBQVFZLEdBQUc7UUFDNUMsTUFBTUMsT0FBT0MsU0FBU0osYUFBYUssR0FBRyxDQUFDLFdBQVc7UUFDbEQsTUFBTUMsWUFBWSxJQUFJQztRQUN0QkQsVUFBVUUsT0FBTyxDQUFDRixVQUFVRyxPQUFPLEtBQUtOO1FBRXhDLGdEQUFnRDtRQUNoRCxNQUFNLEVBQUVYLE1BQU1rQixpQkFBaUIsRUFBRUMsT0FBT0MsYUFBYSxFQUFFLEdBQUcsTUFBTXJCLFNBQzdEc0IsSUFBSSxDQUFDLGdCQUNMQyxNQUFNLENBQUMsTUFBTTtZQUFFSCxPQUFPO1FBQVEsR0FDOUJJLEVBQUUsQ0FBQyxXQUFXdEIsS0FBS3VCLEVBQUUsRUFDckJDLEdBQUcsQ0FBQyxxQkFBcUJYLFVBQVVZLFdBQVc7UUFFakQsNENBQTRDO1FBQzVDLE1BQU0sRUFBRTFCLE1BQU0yQixrQkFBa0IsRUFBRVIsT0FBT1MsY0FBYyxFQUFFLEdBQUcsTUFBTTdCLFNBQy9Ec0IsSUFBSSxDQUFDLGdCQUNMQyxNQUFNLENBQUMsbUVBQW1FO1lBQUVILE9BQU87UUFBUSxHQUMzRkksRUFBRSxDQUFDLFdBQVd0QixLQUFLdUIsRUFBRSxFQUNyQkMsR0FBRyxDQUFDLHFCQUFxQlgsVUFBVVksV0FBVyxJQUM5Q0QsR0FBRyxDQUFDLGVBQWU7UUFFdEIsdUJBQXVCO1FBQ3ZCLE1BQU1JLFlBQVlULGdCQUFnQixJQUFJLGlCQUFrQkEsZ0JBQWlCLE1BQU07UUFFL0UscUNBQXFDO1FBQ3JDLE1BQU1VLHNCQUFzQixDQUFDO1FBQzdCLE1BQU1DLHVCQUF1QixDQUFDO1FBQzlCLE1BQU1DLGdCQUFnQixFQUFFO1FBRXhCLElBQUlMLG9CQUFvQjtZQUN0QkEsbUJBQW1CTSxPQUFPLENBQUNDLENBQUFBO2dCQUN6Qix3QkFBd0I7Z0JBQ3hCLE1BQU1DLGFBQWFELElBQUlFLFdBQVc7Z0JBQ2xDTixtQkFBbUIsQ0FBQ0ssV0FBVyxHQUFHLENBQUNMLG1CQUFtQixDQUFDSyxXQUFXLElBQUksS0FBSztnQkFFM0UseUJBQXlCO2dCQUN6QixNQUFNRSxjQUFjSCxJQUFJSSxZQUFZLElBQUk7Z0JBQ3hDUCxvQkFBb0IsQ0FBQ00sWUFBWSxHQUFHLENBQUNOLG9CQUFvQixDQUFDTSxZQUFZLElBQUksS0FBSztnQkFFL0UseUJBQXlCO2dCQUN6QixJQUFJSCxJQUFJSyxhQUFhLEVBQUU7b0JBQ3JCUCxjQUFjUSxJQUFJLENBQUM7d0JBQ2pCQyxTQUFTUCxJQUFJSyxhQUFhO3dCQUMxQkcsUUFBUVIsSUFBSUksWUFBWTt3QkFDeEJILFlBQVlELElBQUlFLFdBQVc7d0JBQzNCTyxXQUFXVCxJQUFJVSxpQkFBaUI7b0JBQ2xDO2dCQUNGO1lBQ0Y7UUFDRjtRQUVBLDZCQUE2QjtRQUM3QixNQUFNQyxpQkFBaUJDLE9BQU9DLE9BQU8sQ0FBQ2pCLHFCQUNuQ2tCLElBQUksQ0FBQyxDQUFDLEdBQUVDLEVBQUUsRUFBRSxHQUFFQyxFQUFFLEdBQUtBLElBQUlELEdBQ3pCRSxLQUFLLENBQUMsR0FBRyxJQUNUQyxHQUFHLENBQUMsQ0FBQyxDQUFDQyxNQUFNbEMsTUFBTSxHQUFNO2dCQUN2QmdCLFlBQVl2QixTQUFTeUM7Z0JBQ3JCbEM7Z0JBQ0FtQyxhQUFhQyx5QkFBeUIzQyxTQUFTeUM7WUFDakQ7UUFFRix3QkFBd0I7UUFDeEIsTUFBTUcsa0JBQWtCVixPQUFPQyxPQUFPLENBQUNoQixzQkFDcENpQixJQUFJLENBQUMsQ0FBQyxHQUFFQyxFQUFFLEVBQUUsR0FBRUMsRUFBRSxHQUFLQSxJQUFJRCxHQUN6QkUsS0FBSyxDQUFDLEdBQUcsSUFDVEMsR0FBRyxDQUFDLENBQUMsQ0FBQ1YsUUFBUXZCLE1BQU0sR0FBTTtnQkFBRXVCO2dCQUFRdkI7WUFBTTtRQUU3QyxrREFBa0Q7UUFDbEQsTUFBTXNDLGNBQWMsQ0FBQztRQUNyQixJQUFJOUIsb0JBQW9CO1lBQ3RCQSxtQkFBbUJNLE9BQU8sQ0FBQ0MsQ0FBQUE7Z0JBQ3pCLE1BQU13QixPQUFPLElBQUkzQyxLQUFLbUIsSUFBSVUsaUJBQWlCLEVBQUVsQixXQUFXLEdBQUdpQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7Z0JBQ3hFRixXQUFXLENBQUNDLEtBQUssR0FBRyxDQUFDRCxXQUFXLENBQUNDLEtBQUssSUFBSSxLQUFLO1lBQ2pEO1FBQ0Y7UUFFQSxnQ0FBZ0M7UUFDaEMsTUFBTUUsc0JBQXNCLEVBQUU7UUFDOUIsSUFBSyxJQUFJQyxJQUFJbEQsT0FBTyxHQUFHa0QsS0FBSyxHQUFHQSxJQUFLO1lBQ2xDLE1BQU1ILE9BQU8sSUFBSTNDO1lBQ2pCMkMsS0FBSzFDLE9BQU8sQ0FBQzBDLEtBQUt6QyxPQUFPLEtBQUs0QztZQUM5QixNQUFNQyxVQUFVSixLQUFLaEMsV0FBVyxHQUFHaUMsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1lBQ2hEQyxvQkFBb0JwQixJQUFJLENBQUM7Z0JBQ3ZCa0IsTUFBTUk7Z0JBQ05DLFFBQVFOLFdBQVcsQ0FBQ0ssUUFBUSxJQUFJO1lBQ2xDO1FBQ0Y7UUFFQSxzQ0FBc0M7UUFDdEMsTUFBTUUsZUFBZWhDLGNBQ2xCZ0IsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU0sSUFBSW5DLEtBQUttQyxFQUFFUCxTQUFTLEVBQUVzQixPQUFPLEtBQUssSUFBSWxELEtBQUtrQyxFQUFFTixTQUFTLEVBQUVzQixPQUFPLElBQzlFZCxLQUFLLENBQUMsR0FBRztRQUVaLDREQUE0RDtRQUM1RCxNQUFNZSxvQkFBb0IsSUFBSW5ELEtBQUtEO1FBQ25Db0Qsa0JBQWtCbEQsT0FBTyxDQUFDa0Qsa0JBQWtCakQsT0FBTyxLQUFLTjtRQUV4RCxNQUFNLEVBQUVRLE9BQU9nRCxxQkFBcUIsRUFBRSxHQUFHLE1BQU1wRSxTQUM1Q3NCLElBQUksQ0FBQyxnQkFDTEMsTUFBTSxDQUFDLE1BQU07WUFBRUgsT0FBTztRQUFRLEdBQzlCSSxFQUFFLENBQUMsV0FBV3RCLEtBQUt1QixFQUFFLEVBQ3JCQyxHQUFHLENBQUMscUJBQXFCeUMsa0JBQWtCeEMsV0FBVyxJQUN0RDBDLEVBQUUsQ0FBQyxxQkFBcUJ0RCxVQUFVWSxXQUFXO1FBRWhELE1BQU0sRUFBRVAsT0FBT2tELHNCQUFzQixFQUFFLEdBQUcsTUFBTXRFLFNBQzdDc0IsSUFBSSxDQUFDLGdCQUNMQyxNQUFNLENBQUMsTUFBTTtZQUFFSCxPQUFPO1FBQVEsR0FDOUJJLEVBQUUsQ0FBQyxXQUFXdEIsS0FBS3VCLEVBQUUsRUFDckJDLEdBQUcsQ0FBQyxxQkFBcUJ5QyxrQkFBa0J4QyxXQUFXLElBQ3REMEMsRUFBRSxDQUFDLHFCQUFxQnRELFVBQVVZLFdBQVcsSUFDN0NELEdBQUcsQ0FBQyxlQUFlO1FBRXRCLE1BQU02QyxvQkFBb0JILHdCQUF3QixJQUFJLHlCQUEwQkEsd0JBQXlCLE1BQU07UUFDL0csTUFBTUksaUJBQWlCMUMsWUFBWXlDO1FBRW5DLE9BQU8zRSxxREFBWUEsQ0FBQ1csSUFBSSxDQUFDO1lBQ3ZCa0UsU0FBUztZQUNUeEUsTUFBTTtnQkFDSixrQkFBa0I7Z0JBQ2xCb0IsZUFBZUEsaUJBQWlCO2dCQUNoQ1EsZ0JBQWdCQSxrQkFBa0I7Z0JBQ2xDQztnQkFDQTBDO2dCQUVBLGFBQWE7Z0JBQ2J6QyxxQkFBcUJlO2dCQUNyQmQsc0JBQXNCeUI7Z0JBRXRCLG1CQUFtQjtnQkFDbkJJO2dCQUVBLGdCQUFnQjtnQkFDaEJJO2dCQUVBLGNBQWM7Z0JBQ2RTLFFBQVEsR0FBRzlELEtBQUssS0FBSyxDQUFDO2dCQUN0QkcsV0FBV0EsVUFBVVksV0FBVztnQkFDaENnRCxTQUFTLElBQUkzRCxPQUFPVyxXQUFXO1lBQ2pDO1FBQ0Y7SUFFRixFQUFFLE9BQU94QixPQUFPO1FBQ2R5RSxRQUFRekUsS0FBSyxDQUFDLDhCQUE4QkE7UUFDNUMsT0FBT1AscURBQVlBLENBQUNXLElBQUksQ0FBQztZQUN2QkosT0FBTztZQUNQMEUsU0FBUzFFLGlCQUFpQjJFLFFBQVEzRSxNQUFNdUMsT0FBTyxHQUFHO1FBQ3BELEdBQUc7WUFBRWxDLFFBQVE7UUFBSTtJQUNuQjtBQUNGO0FBRUEsU0FBU2dELHlCQUF5QnBCLFVBQWtCO0lBQ2xELE1BQU0yQyxlQUFlO1FBQ25CLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztJQUNQO0lBRUEsT0FBT0EsWUFBWSxDQUFDM0MsV0FBVyxJQUFJO0FBQ3JDIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxhcGlcXGFuYWx5dGljc1xcZXJyb3JzXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgY3JlYXRlU3VwYWJhc2VTZXJ2ZXJDbGllbnRGcm9tUmVxdWVzdCB9IGZyb20gJ0AvbGliL3N1cGFiYXNlL3NlcnZlcic7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZVN1cGFiYXNlU2VydmVyQ2xpZW50RnJvbVJlcXVlc3QocmVxdWVzdCk7XG4gICAgXG4gICAgLy8gR2V0IGF1dGhlbnRpY2F0ZWQgdXNlclxuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0sIGVycm9yOiBhdXRoRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuICAgIGlmIChhdXRoRXJyb3IgfHwgIXVzZXIpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnVW5hdXRob3JpemVkJyB9LCB7IHN0YXR1czogNDAxIH0pO1xuICAgIH1cblxuICAgIGNvbnN0IHsgc2VhcmNoUGFyYW1zIH0gPSBuZXcgVVJMKHJlcXVlc3QudXJsKTtcbiAgICBjb25zdCBkYXlzID0gcGFyc2VJbnQoc2VhcmNoUGFyYW1zLmdldCgnZGF5cycpIHx8ICczMCcpO1xuICAgIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKCk7XG4gICAgc3RhcnREYXRlLnNldERhdGUoc3RhcnREYXRlLmdldERhdGUoKSAtIGRheXMpO1xuXG4gICAgLy8gR2V0IHRvdGFsIHJlcXVlc3RzIGZvciBlcnJvciByYXRlIGNhbGN1bGF0aW9uXG4gICAgY29uc3QgeyBkYXRhOiB0b3RhbFJlcXVlc3RzRGF0YSwgY291bnQ6IHRvdGFsUmVxdWVzdHMgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgncmVxdWVzdF9sb2dzJylcbiAgICAgIC5zZWxlY3QoJ2lkJywgeyBjb3VudDogJ2V4YWN0JyB9KVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5ndGUoJ3JlcXVlc3RfdGltZXN0YW1wJywgc3RhcnREYXRlLnRvSVNPU3RyaW5nKCkpO1xuXG4gICAgLy8gR2V0IGZhaWxlZCByZXF1ZXN0cyAoc3RhdHVzIGNvZGVzID49IDQwMClcbiAgICBjb25zdCB7IGRhdGE6IGZhaWxlZFJlcXVlc3RzRGF0YSwgY291bnQ6IGZhaWxlZFJlcXVlc3RzIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3JlcXVlc3RfbG9ncycpXG4gICAgICAuc2VsZWN0KCdpZCwgc3RhdHVzX2NvZGUsIGVycm9yX21lc3NhZ2UsIGVycm9yX3NvdXJjZSwgcmVxdWVzdF90aW1lc3RhbXAnLCB7IGNvdW50OiAnZXhhY3QnIH0pXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgICAgLmd0ZSgncmVxdWVzdF90aW1lc3RhbXAnLCBzdGFydERhdGUudG9JU09TdHJpbmcoKSlcbiAgICAgIC5ndGUoJ3N0YXR1c19jb2RlJywgNDAwKTtcblxuICAgIC8vIENhbGN1bGF0ZSBlcnJvciByYXRlXG4gICAgY29uc3QgZXJyb3JSYXRlID0gdG90YWxSZXF1ZXN0cyA+IDAgPyAoZmFpbGVkUmVxdWVzdHMgLyB0b3RhbFJlcXVlc3RzKSAqIDEwMCA6IDA7XG5cbiAgICAvLyBHZXQgZXJyb3IgYnJlYWtkb3duIGJ5IHN0YXR1cyBjb2RlXG4gICAgY29uc3Qgc3RhdHVzQ29kZUJyZWFrZG93biA9IHt9O1xuICAgIGNvbnN0IGVycm9yU291cmNlQnJlYWtkb3duID0ge307XG4gICAgY29uc3QgZXJyb3JNZXNzYWdlcyA9IFtdO1xuXG4gICAgaWYgKGZhaWxlZFJlcXVlc3RzRGF0YSkge1xuICAgICAgZmFpbGVkUmVxdWVzdHNEYXRhLmZvckVhY2gobG9nID0+IHtcbiAgICAgICAgLy8gU3RhdHVzIGNvZGUgYnJlYWtkb3duXG4gICAgICAgIGNvbnN0IHN0YXR1c0NvZGUgPSBsb2cuc3RhdHVzX2NvZGU7XG4gICAgICAgIHN0YXR1c0NvZGVCcmVha2Rvd25bc3RhdHVzQ29kZV0gPSAoc3RhdHVzQ29kZUJyZWFrZG93bltzdGF0dXNDb2RlXSB8fCAwKSArIDE7XG5cbiAgICAgICAgLy8gRXJyb3Igc291cmNlIGJyZWFrZG93blxuICAgICAgICBjb25zdCBlcnJvclNvdXJjZSA9IGxvZy5lcnJvcl9zb3VyY2UgfHwgJ3Vua25vd24nO1xuICAgICAgICBlcnJvclNvdXJjZUJyZWFrZG93bltlcnJvclNvdXJjZV0gPSAoZXJyb3JTb3VyY2VCcmVha2Rvd25bZXJyb3JTb3VyY2VdIHx8IDApICsgMTtcblxuICAgICAgICAvLyBDb2xsZWN0IGVycm9yIG1lc3NhZ2VzXG4gICAgICAgIGlmIChsb2cuZXJyb3JfbWVzc2FnZSkge1xuICAgICAgICAgIGVycm9yTWVzc2FnZXMucHVzaCh7XG4gICAgICAgICAgICBtZXNzYWdlOiBsb2cuZXJyb3JfbWVzc2FnZSxcbiAgICAgICAgICAgIHNvdXJjZTogbG9nLmVycm9yX3NvdXJjZSxcbiAgICAgICAgICAgIHN0YXR1c0NvZGU6IGxvZy5zdGF0dXNfY29kZSxcbiAgICAgICAgICAgIHRpbWVzdGFtcDogbG9nLnJlcXVlc3RfdGltZXN0YW1wXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIEdldCB0b3AgZXJyb3Igc3RhdHVzIGNvZGVzXG4gICAgY29uc3QgdG9wU3RhdHVzQ29kZXMgPSBPYmplY3QuZW50cmllcyhzdGF0dXNDb2RlQnJlYWtkb3duKVxuICAgICAgLnNvcnQoKFssYV0sIFssYl0pID0+IGIgLSBhKVxuICAgICAgLnNsaWNlKDAsIDEwKVxuICAgICAgLm1hcCgoW2NvZGUsIGNvdW50XSkgPT4gKHsgXG4gICAgICAgIHN0YXR1c0NvZGU6IHBhcnNlSW50KGNvZGUpLCBcbiAgICAgICAgY291bnQsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBnZXRTdGF0dXNDb2RlRGVzY3JpcHRpb24ocGFyc2VJbnQoY29kZSkpXG4gICAgICB9KSk7XG5cbiAgICAvLyBHZXQgdG9wIGVycm9yIHNvdXJjZXNcbiAgICBjb25zdCB0b3BFcnJvclNvdXJjZXMgPSBPYmplY3QuZW50cmllcyhlcnJvclNvdXJjZUJyZWFrZG93bilcbiAgICAgIC5zb3J0KChbLGFdLCBbLGJdKSA9PiBiIC0gYSlcbiAgICAgIC5zbGljZSgwLCAxMClcbiAgICAgIC5tYXAoKFtzb3VyY2UsIGNvdW50XSkgPT4gKHsgc291cmNlLCBjb3VudCB9KSk7XG5cbiAgICAvLyBHZXQgZXJyb3IgdHJlbmRzIG92ZXIgdGltZSAoZGFpbHkgZXJyb3IgY291bnRzKVxuICAgIGNvbnN0IGRhaWx5RXJyb3JzID0ge307XG4gICAgaWYgKGZhaWxlZFJlcXVlc3RzRGF0YSkge1xuICAgICAgZmFpbGVkUmVxdWVzdHNEYXRhLmZvckVhY2gobG9nID0+IHtcbiAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGxvZy5yZXF1ZXN0X3RpbWVzdGFtcCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xuICAgICAgICBkYWlseUVycm9yc1tkYXRlXSA9IChkYWlseUVycm9yc1tkYXRlXSB8fCAwKSArIDE7XG4gICAgICB9KTtcbiAgICB9XG5cbiAgICAvLyBDb252ZXJ0IHRvIHRpbWUgc2VyaWVzIGZvcm1hdFxuICAgIGNvbnN0IGVycm9yVGltZVNlcmllc0RhdGEgPSBbXTtcbiAgICBmb3IgKGxldCBpID0gZGF5cyAtIDE7IGkgPj0gMDsgaS0tKSB7XG4gICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKTtcbiAgICAgIGRhdGUuc2V0RGF0ZShkYXRlLmdldERhdGUoKSAtIGkpO1xuICAgICAgY29uc3QgZGF0ZVN0ciA9IGRhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xuICAgICAgZXJyb3JUaW1lU2VyaWVzRGF0YS5wdXNoKHtcbiAgICAgICAgZGF0ZTogZGF0ZVN0cixcbiAgICAgICAgZXJyb3JzOiBkYWlseUVycm9yc1tkYXRlU3RyXSB8fCAwXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICAvLyBHZXQgcmVjZW50IGVycm9yIG1lc3NhZ2VzIChsYXN0IDIwKVxuICAgIGNvbnN0IHJlY2VudEVycm9ycyA9IGVycm9yTWVzc2FnZXNcbiAgICAgIC5zb3J0KChhLCBiKSA9PiBuZXcgRGF0ZShiLnRpbWVzdGFtcCkuZ2V0VGltZSgpIC0gbmV3IERhdGUoYS50aW1lc3RhbXApLmdldFRpbWUoKSlcbiAgICAgIC5zbGljZSgwLCAyMCk7XG5cbiAgICAvLyBDYWxjdWxhdGUgZXJyb3IgcmF0ZSB0cmVuZCAoY29tcGFyZSB3aXRoIHByZXZpb3VzIHBlcmlvZClcbiAgICBjb25zdCBwcmV2aW91c1N0YXJ0RGF0ZSA9IG5ldyBEYXRlKHN0YXJ0RGF0ZSk7XG4gICAgcHJldmlvdXNTdGFydERhdGUuc2V0RGF0ZShwcmV2aW91c1N0YXJ0RGF0ZS5nZXREYXRlKCkgLSBkYXlzKTtcblxuICAgIGNvbnN0IHsgY291bnQ6IHByZXZpb3VzVG90YWxSZXF1ZXN0cyB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdyZXF1ZXN0X2xvZ3MnKVxuICAgICAgLnNlbGVjdCgnaWQnLCB7IGNvdW50OiAnZXhhY3QnIH0pXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgICAgLmd0ZSgncmVxdWVzdF90aW1lc3RhbXAnLCBwcmV2aW91c1N0YXJ0RGF0ZS50b0lTT1N0cmluZygpKVxuICAgICAgLmx0KCdyZXF1ZXN0X3RpbWVzdGFtcCcsIHN0YXJ0RGF0ZS50b0lTT1N0cmluZygpKTtcblxuICAgIGNvbnN0IHsgY291bnQ6IHByZXZpb3VzRmFpbGVkUmVxdWVzdHMgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgncmVxdWVzdF9sb2dzJylcbiAgICAgIC5zZWxlY3QoJ2lkJywgeyBjb3VudDogJ2V4YWN0JyB9KVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5ndGUoJ3JlcXVlc3RfdGltZXN0YW1wJywgcHJldmlvdXNTdGFydERhdGUudG9JU09TdHJpbmcoKSlcbiAgICAgIC5sdCgncmVxdWVzdF90aW1lc3RhbXAnLCBzdGFydERhdGUudG9JU09TdHJpbmcoKSlcbiAgICAgIC5ndGUoJ3N0YXR1c19jb2RlJywgNDAwKTtcblxuICAgIGNvbnN0IHByZXZpb3VzRXJyb3JSYXRlID0gcHJldmlvdXNUb3RhbFJlcXVlc3RzID4gMCA/IChwcmV2aW91c0ZhaWxlZFJlcXVlc3RzIC8gcHJldmlvdXNUb3RhbFJlcXVlc3RzKSAqIDEwMCA6IDA7XG4gICAgY29uc3QgZXJyb3JSYXRlVHJlbmQgPSBlcnJvclJhdGUgLSBwcmV2aW91c0Vycm9yUmF0ZTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZGF0YToge1xuICAgICAgICAvLyBTdW1tYXJ5IG1ldHJpY3NcbiAgICAgICAgdG90YWxSZXF1ZXN0czogdG90YWxSZXF1ZXN0cyB8fCAwLFxuICAgICAgICBmYWlsZWRSZXF1ZXN0czogZmFpbGVkUmVxdWVzdHMgfHwgMCxcbiAgICAgICAgZXJyb3JSYXRlLFxuICAgICAgICBlcnJvclJhdGVUcmVuZCxcbiAgICAgICAgXG4gICAgICAgIC8vIEJyZWFrZG93bnNcbiAgICAgICAgc3RhdHVzQ29kZUJyZWFrZG93bjogdG9wU3RhdHVzQ29kZXMsXG4gICAgICAgIGVycm9yU291cmNlQnJlYWtkb3duOiB0b3BFcnJvclNvdXJjZXMsXG4gICAgICAgIFxuICAgICAgICAvLyBUaW1lIHNlcmllcyBkYXRhXG4gICAgICAgIGVycm9yVGltZVNlcmllc0RhdGEsXG4gICAgICAgIFxuICAgICAgICAvLyBSZWNlbnQgZXJyb3JzXG4gICAgICAgIHJlY2VudEVycm9ycyxcbiAgICAgICAgXG4gICAgICAgIC8vIFBlcmlvZCBpbmZvXG4gICAgICAgIHBlcmlvZDogYCR7ZGF5c30gZGF5c2AsXG4gICAgICAgIHN0YXJ0RGF0ZTogc3RhcnREYXRlLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIGVuZERhdGU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfVxuICAgIH0pO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gZXJyb3JzIGFuYWx5dGljczonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIGVycm9yOiAnRmFpbGVkIHRvIGZldGNoIGVycm9ycyBhbmFseXRpY3MnLFxuICAgICAgZGV0YWlsczogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcidcbiAgICB9LCB7IHN0YXR1czogNTAwIH0pO1xuICB9XG59XG5cbmZ1bmN0aW9uIGdldFN0YXR1c0NvZGVEZXNjcmlwdGlvbihzdGF0dXNDb2RlOiBudW1iZXIpOiBzdHJpbmcge1xuICBjb25zdCBkZXNjcmlwdGlvbnMgPSB7XG4gICAgNDAwOiAnQmFkIFJlcXVlc3QnLFxuICAgIDQwMTogJ1VuYXV0aG9yaXplZCcsXG4gICAgNDAzOiAnRm9yYmlkZGVuJyxcbiAgICA0MDQ6ICdOb3QgRm91bmQnLFxuICAgIDQwNTogJ01ldGhvZCBOb3QgQWxsb3dlZCcsXG4gICAgNDA5OiAnQ29uZmxpY3QnLFxuICAgIDQyMjogJ1VucHJvY2Vzc2FibGUgRW50aXR5JyxcbiAgICA0Mjk6ICdUb28gTWFueSBSZXF1ZXN0cycsXG4gICAgNTAwOiAnSW50ZXJuYWwgU2VydmVyIEVycm9yJyxcbiAgICA1MDI6ICdCYWQgR2F0ZXdheScsXG4gICAgNTAzOiAnU2VydmljZSBVbmF2YWlsYWJsZScsXG4gICAgNTA0OiAnR2F0ZXdheSBUaW1lb3V0J1xuICB9O1xuICBcbiAgcmV0dXJuIGRlc2NyaXB0aW9uc1tzdGF0dXNDb2RlXSB8fCAnVW5rbm93biBFcnJvcic7XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiY3JlYXRlU3VwYWJhc2VTZXJ2ZXJDbGllbnRGcm9tUmVxdWVzdCIsIkdFVCIsInJlcXVlc3QiLCJzdXBhYmFzZSIsImRhdGEiLCJ1c2VyIiwiZXJyb3IiLCJhdXRoRXJyb3IiLCJhdXRoIiwiZ2V0VXNlciIsImpzb24iLCJzdGF0dXMiLCJzZWFyY2hQYXJhbXMiLCJVUkwiLCJ1cmwiLCJkYXlzIiwicGFyc2VJbnQiLCJnZXQiLCJzdGFydERhdGUiLCJEYXRlIiwic2V0RGF0ZSIsImdldERhdGUiLCJ0b3RhbFJlcXVlc3RzRGF0YSIsImNvdW50IiwidG90YWxSZXF1ZXN0cyIsImZyb20iLCJzZWxlY3QiLCJlcSIsImlkIiwiZ3RlIiwidG9JU09TdHJpbmciLCJmYWlsZWRSZXF1ZXN0c0RhdGEiLCJmYWlsZWRSZXF1ZXN0cyIsImVycm9yUmF0ZSIsInN0YXR1c0NvZGVCcmVha2Rvd24iLCJlcnJvclNvdXJjZUJyZWFrZG93biIsImVycm9yTWVzc2FnZXMiLCJmb3JFYWNoIiwibG9nIiwic3RhdHVzQ29kZSIsInN0YXR1c19jb2RlIiwiZXJyb3JTb3VyY2UiLCJlcnJvcl9zb3VyY2UiLCJlcnJvcl9tZXNzYWdlIiwicHVzaCIsIm1lc3NhZ2UiLCJzb3VyY2UiLCJ0aW1lc3RhbXAiLCJyZXF1ZXN0X3RpbWVzdGFtcCIsInRvcFN0YXR1c0NvZGVzIiwiT2JqZWN0IiwiZW50cmllcyIsInNvcnQiLCJhIiwiYiIsInNsaWNlIiwibWFwIiwiY29kZSIsImRlc2NyaXB0aW9uIiwiZ2V0U3RhdHVzQ29kZURlc2NyaXB0aW9uIiwidG9wRXJyb3JTb3VyY2VzIiwiZGFpbHlFcnJvcnMiLCJkYXRlIiwic3BsaXQiLCJlcnJvclRpbWVTZXJpZXNEYXRhIiwiaSIsImRhdGVTdHIiLCJlcnJvcnMiLCJyZWNlbnRFcnJvcnMiLCJnZXRUaW1lIiwicHJldmlvdXNTdGFydERhdGUiLCJwcmV2aW91c1RvdGFsUmVxdWVzdHMiLCJsdCIsInByZXZpb3VzRmFpbGVkUmVxdWVzdHMiLCJwcmV2aW91c0Vycm9yUmF0ZSIsImVycm9yUmF0ZVRyZW5kIiwic3VjY2VzcyIsInBlcmlvZCIsImVuZERhdGUiLCJjb25zb2xlIiwiZGV0YWlscyIsIkVycm9yIiwiZGVzY3JpcHRpb25zIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analytics/errors/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Ferrors%2Froute&page=%2Fapi%2Fanalytics%2Ferrors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Ferrors%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();