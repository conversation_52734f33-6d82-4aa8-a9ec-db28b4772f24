/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analytics/feedback/route";
exports.ids = ["app/api/analytics/feedback/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Ffeedback%2Froute&page=%2Fapi%2Fanalytics%2Ffeedback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Ffeedback%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Ffeedback%2Froute&page=%2Fapi%2Fanalytics%2Ffeedback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Ffeedback%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_analytics_feedback_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analytics/feedback/route.ts */ \"(rsc)/./src/app/api/analytics/feedback/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analytics/feedback/route\",\n        pathname: \"/api/analytics/feedback\",\n        filename: \"route\",\n        bundlePath: \"app/api/analytics/feedback/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\analytics\\\\feedback\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_analytics_feedback_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Ffeedback%2Froute&page=%2Fapi%2Fanalytics%2Ffeedback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Ffeedback%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/analytics/feedback/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/analytics/feedback/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n        // Get authenticated user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const days = parseInt(searchParams.get('days') || '30');\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - days);\n        // Get all feedback data\n        const { data: feedbackData, count: totalFeedback } = await supabase.from('routing_quality_metrics').select('*', {\n            count: 'exact'\n        }).eq('user_id', user.id).gte('created_at', startDate.toISOString()).order('created_at', {\n            ascending: true\n        });\n        if (!feedbackData || feedbackData.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    totalFeedback: 0,\n                    averageRating: 0,\n                    averageQualityScore: 0,\n                    ratingTrend: 0,\n                    qualityTrend: 0,\n                    ratingDistribution: [],\n                    qualityDistribution: [],\n                    feedbackByProvider: [],\n                    feedbackByModel: [],\n                    behavioralSignals: {\n                        copiedResponses: 0,\n                        regeneratedResponses: 0,\n                        followupQuestions: 0,\n                        continuedConversations: 0\n                    },\n                    feedbackTimeSeriesData: [],\n                    taskCategoryBreakdown: [],\n                    complexityBreakdown: [],\n                    period: `${days} days`,\n                    startDate: startDate.toISOString(),\n                    endDate: new Date().toISOString()\n                }\n            });\n        }\n        // Calculate summary metrics\n        const totalRating = feedbackData.reduce((sum, item)=>sum + (item.user_rating || 0), 0);\n        const totalQualityScore = feedbackData.reduce((sum, item)=>sum + (item.quality_score || 0), 0);\n        const averageRating = totalFeedback > 0 ? totalRating / totalFeedback : 0;\n        const averageQualityScore = totalFeedback > 0 ? totalQualityScore / totalFeedback : 0;\n        // Calculate rating distribution\n        const ratingCounts = {\n            1: 0,\n            2: 0,\n            3: 0,\n            4: 0,\n            5: 0\n        };\n        feedbackData.forEach((item)=>{\n            const rating = item.user_rating;\n            if (rating >= 1 && rating <= 5) {\n                ratingCounts[rating]++;\n            }\n        });\n        const ratingDistribution = Object.entries(ratingCounts).map(([rating, count])=>({\n                rating: parseInt(rating),\n                count,\n                percentage: totalFeedback > 0 ? count / totalFeedback * 100 : 0\n            }));\n        // Calculate quality score distribution\n        const qualityRanges = {\n            'Poor (0-2)': 0,\n            'Fair (2-4)': 0,\n            'Good (4-6)': 0,\n            'Very Good (6-8)': 0,\n            'Excellent (8-10)': 0\n        };\n        feedbackData.forEach((item)=>{\n            const score = item.quality_score || 0;\n            if (score < 2) qualityRanges['Poor (0-2)']++;\n            else if (score < 4) qualityRanges['Fair (2-4)']++;\n            else if (score < 6) qualityRanges['Good (4-6)']++;\n            else if (score < 8) qualityRanges['Very Good (6-8)']++;\n            else qualityRanges['Excellent (8-10)']++;\n        });\n        const qualityDistribution = Object.entries(qualityRanges).map(([range, count])=>({\n                range,\n                count,\n                percentage: totalFeedback > 0 ? count / totalFeedback * 100 : 0\n            }));\n        // Feedback by provider\n        const providerFeedback = {};\n        feedbackData.forEach((item)=>{\n            const provider = item.provider || 'unknown';\n            if (!providerFeedback[provider]) {\n                providerFeedback[provider] = {\n                    count: 0,\n                    totalRating: 0,\n                    totalQuality: 0\n                };\n            }\n            providerFeedback[provider].count++;\n            providerFeedback[provider].totalRating += item.user_rating || 0;\n            providerFeedback[provider].totalQuality += item.quality_score || 0;\n        });\n        const feedbackByProvider = Object.entries(providerFeedback).map(([provider, data])=>({\n                provider,\n                count: data.count,\n                averageRating: data.count > 0 ? data.totalRating / data.count : 0,\n                averageQuality: data.count > 0 ? data.totalQuality / data.count : 0\n            })).sort((a, b)=>b.count - a.count);\n        // Feedback by model\n        const modelFeedback = {};\n        feedbackData.forEach((item)=>{\n            const model = item.model_used || 'unknown';\n            if (!modelFeedback[model]) {\n                modelFeedback[model] = {\n                    count: 0,\n                    totalRating: 0,\n                    totalQuality: 0\n                };\n            }\n            modelFeedback[model].count++;\n            modelFeedback[model].totalRating += item.user_rating || 0;\n            modelFeedback[model].totalQuality += item.quality_score || 0;\n        });\n        const feedbackByModel = Object.entries(modelFeedback).map(([model, data])=>({\n                model,\n                count: data.count,\n                averageRating: data.count > 0 ? data.totalRating / data.count : 0,\n                averageQuality: data.count > 0 ? data.totalQuality / data.count : 0\n            })).sort((a, b)=>b.count - a.count).slice(0, 10);\n        // Behavioral signals\n        const behavioralSignals = {\n            copiedResponses: feedbackData.filter((item)=>item.user_copied_response).length,\n            regeneratedResponses: feedbackData.filter((item)=>item.user_regenerated).length,\n            followupQuestions: feedbackData.filter((item)=>item.user_asked_followup).length,\n            continuedConversations: feedbackData.filter((item)=>item.conversation_continued).length\n        };\n        // Time series data (daily feedback)\n        const dailyFeedback = {};\n        feedbackData.forEach((item)=>{\n            const date = new Date(item.created_at).toISOString().split('T')[0];\n            if (!dailyFeedback[date]) {\n                dailyFeedback[date] = {\n                    count: 0,\n                    totalRating: 0,\n                    totalQuality: 0\n                };\n            }\n            dailyFeedback[date].count++;\n            dailyFeedback[date].totalRating += item.user_rating || 0;\n            dailyFeedback[date].totalQuality += item.quality_score || 0;\n        });\n        const feedbackTimeSeriesData = [];\n        for(let i = days - 1; i >= 0; i--){\n            const date = new Date();\n            date.setDate(date.getDate() - i);\n            const dateStr = date.toISOString().split('T')[0];\n            const dayData = dailyFeedback[dateStr] || {\n                count: 0,\n                totalRating: 0,\n                totalQuality: 0\n            };\n            feedbackTimeSeriesData.push({\n                date: dateStr,\n                count: dayData.count,\n                averageRating: dayData.count > 0 ? dayData.totalRating / dayData.count : 0,\n                averageQuality: dayData.count > 0 ? dayData.totalQuality / dayData.count : 0\n            });\n        }\n        // Task category breakdown\n        const taskCategories = {};\n        feedbackData.forEach((item)=>{\n            const category = item.task_category || 'unknown';\n            if (!taskCategories[category]) {\n                taskCategories[category] = {\n                    count: 0,\n                    totalRating: 0,\n                    totalQuality: 0\n                };\n            }\n            taskCategories[category].count++;\n            taskCategories[category].totalRating += item.user_rating || 0;\n            taskCategories[category].totalQuality += item.quality_score || 0;\n        });\n        const taskCategoryBreakdown = Object.entries(taskCategories).map(([category, data])=>({\n                category,\n                count: data.count,\n                averageRating: data.count > 0 ? data.totalRating / data.count : 0,\n                averageQuality: data.count > 0 ? data.totalQuality / data.count : 0\n            })).sort((a, b)=>b.count - a.count);\n        // Complexity breakdown\n        const complexityLevels = {};\n        feedbackData.forEach((item)=>{\n            const complexity = item.prompt_complexity_level || 0;\n            const level = `Level ${complexity}`;\n            if (!complexityLevels[level]) {\n                complexityLevels[level] = {\n                    count: 0,\n                    totalRating: 0,\n                    totalQuality: 0\n                };\n            }\n            complexityLevels[level].count++;\n            complexityLevels[level].totalRating += item.user_rating || 0;\n            complexityLevels[level].totalQuality += item.quality_score || 0;\n        });\n        const complexityBreakdown = Object.entries(complexityLevels).map(([level, data])=>({\n                level,\n                count: data.count,\n                averageRating: data.count > 0 ? data.totalRating / data.count : 0,\n                averageQuality: data.count > 0 ? data.totalQuality / data.count : 0\n            })).sort((a, b)=>parseInt(a.level.split(' ')[1]) - parseInt(b.level.split(' ')[1]));\n        // Calculate trends (compare with previous period)\n        const previousStartDate = new Date(startDate);\n        previousStartDate.setDate(previousStartDate.getDate() - days);\n        const { data: previousFeedbackData } = await supabase.from('routing_quality_metrics').select('user_rating, quality_score').eq('user_id', user.id).gte('created_at', previousStartDate.toISOString()).lt('created_at', startDate.toISOString());\n        let previousAverageRating = 0;\n        let previousAverageQuality = 0;\n        if (previousFeedbackData && previousFeedbackData.length > 0) {\n            const prevTotalRating = previousFeedbackData.reduce((sum, item)=>sum + (item.user_rating || 0), 0);\n            const prevTotalQuality = previousFeedbackData.reduce((sum, item)=>sum + (item.quality_score || 0), 0);\n            previousAverageRating = prevTotalRating / previousFeedbackData.length;\n            previousAverageQuality = prevTotalQuality / previousFeedbackData.length;\n        }\n        const ratingTrend = averageRating - previousAverageRating;\n        const qualityTrend = averageQualityScore - previousAverageQuality;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                // Summary metrics\n                totalFeedback: totalFeedback || 0,\n                averageRating,\n                averageQualityScore,\n                ratingTrend,\n                qualityTrend,\n                // Distributions\n                ratingDistribution,\n                qualityDistribution,\n                // Breakdowns\n                feedbackByProvider,\n                feedbackByModel,\n                taskCategoryBreakdown,\n                complexityBreakdown,\n                // Behavioral data\n                behavioralSignals,\n                // Time series data\n                feedbackTimeSeriesData,\n                // Period info\n                period: `${days} days`,\n                startDate: startDate.toISOString(),\n                endDate: new Date().toISOString()\n            }\n        });\n    } catch (error) {\n        console.error('Error in feedback analytics:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch feedback analytics',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analytics/feedback/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Ffeedback%2Froute&page=%2Fapi%2Fanalytics%2Ffeedback%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Ffeedback%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();