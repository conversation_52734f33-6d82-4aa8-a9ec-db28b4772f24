import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClientFromRequest(request);
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get semantic cache analytics data
    const { data: cacheAnalyticsData } = await supabase
      .from('semantic_cache_analytics')
      .select('*')
      .eq('user_id', user.id)
      .gte('date', startDate.toISOString().split('T')[0])
      .order('date', { ascending: true });

    // Get total cache entries
    const { data: cacheEntriesData, count: totalCacheEntries } = await supabase
      .from('semantic_cache')
      .select('id', { count: 'exact' })
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString());

    // Calculate cache statistics
    let totalHits = 0;
    let totalMisses = 0;
    let totalCostSaved = 0;
    let totalResponseTimeSaved = 0;
    const dailyCacheData = {};

    if (cacheAnalyticsData && cacheAnalyticsData.length > 0) {
      cacheAnalyticsData.forEach(analytics => {
        totalHits += analytics.cache_hits || 0;
        totalMisses += analytics.cache_misses || 0;
        totalCostSaved += parseFloat(analytics.cost_saved || '0');
        totalResponseTimeSaved += analytics.response_time_saved_ms || 0;
        
        dailyCacheData[analytics.date] = {
          hits: analytics.cache_hits || 0,
          misses: analytics.cache_misses || 0,
          costSaved: parseFloat(analytics.cost_saved || '0'),
          responseTimeSaved: analytics.response_time_saved_ms || 0
        };
      });
    }

    // Calculate cache hit rate
    const totalRequests = totalHits + totalMisses;
    const cacheHitRate = totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0;

    // Get cache entries by tier
    const { data: cacheByTierData } = await supabase
      .from('semantic_cache')
      .select('cache_tier')
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString());

    const cacheByTier = {};
    if (cacheByTierData) {
      cacheByTierData.forEach(entry => {
        const tier = entry.cache_tier || 'unknown';
        cacheByTier[tier] = (cacheByTier[tier] || 0) + 1;
      });
    }

    // Get cache entries by provider
    const { data: cacheByProviderData } = await supabase
      .from('semantic_cache')
      .select('provider_used')
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString());

    const cacheByProvider = {};
    if (cacheByProviderData) {
      cacheByProviderData.forEach(entry => {
        const provider = entry.provider_used || 'unknown';
        cacheByProvider[provider] = (cacheByProvider[provider] || 0) + 1;
      });
    }

    // Convert to time series format for charts
    const cacheTimeSeriesData = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      const dayData = dailyCacheData[dateStr] || { hits: 0, misses: 0, costSaved: 0, responseTimeSaved: 0 };
      
      cacheTimeSeriesData.push({
        date: dateStr,
        hits: dayData.hits,
        misses: dayData.misses,
        hitRate: (dayData.hits + dayData.misses) > 0 ? (dayData.hits / (dayData.hits + dayData.misses)) * 100 : 0,
        costSaved: dayData.costSaved,
        responseTimeSaved: dayData.responseTimeSaved
      });
    }

    // Get top cached models
    const { data: cachedModelsData } = await supabase
      .from('semantic_cache')
      .select('model_used')
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString());

    const modelCacheUsage = {};
    if (cachedModelsData) {
      cachedModelsData.forEach(entry => {
        const model = entry.model_used || 'unknown';
        modelCacheUsage[model] = (modelCacheUsage[model] || 0) + 1;
      });
    }

    const topCachedModels = Object.entries(modelCacheUsage)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([model, count]) => ({ model, count }));

    // Calculate cache efficiency metrics
    const averageResponseTimeSaved = totalHits > 0 ? totalResponseTimeSaved / totalHits : 0;
    const averageCostSavedPerHit = totalHits > 0 ? totalCostSaved / totalHits : 0;

    // Get cache storage usage
    const { data: cacheStorageData } = await supabase
      .from('semantic_cache')
      .select('prompt_text, response_data')
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString());

    let totalStorageBytes = 0;
    if (cacheStorageData) {
      cacheStorageData.forEach(entry => {
        const promptSize = new Blob([entry.prompt_text || '']).size;
        const responseSize = new Blob([JSON.stringify(entry.response_data || {})]).size;
        totalStorageBytes += promptSize + responseSize;
      });
    }

    // Calculate cache trend (compare with previous period)
    const previousStartDate = new Date(startDate);
    previousStartDate.setDate(previousStartDate.getDate() - days);

    const { data: previousCacheData } = await supabase
      .from('semantic_cache_analytics')
      .select('cache_hits, cache_misses')
      .eq('user_id', user.id)
      .gte('date', previousStartDate.toISOString().split('T')[0])
      .lt('date', startDate.toISOString().split('T')[0]);

    let previousHits = 0;
    let previousMisses = 0;
    if (previousCacheData) {
      previousCacheData.forEach(analytics => {
        previousHits += analytics.cache_hits || 0;
        previousMisses += analytics.cache_misses || 0;
      });
    }

    const previousTotalRequests = previousHits + previousMisses;
    const previousHitRate = previousTotalRequests > 0 ? (previousHits / previousTotalRequests) * 100 : 0;
    const hitRateTrend = cacheHitRate - previousHitRate;

    return NextResponse.json({
      success: true,
      data: {
        // Summary metrics
        totalCacheEntries: totalCacheEntries || 0,
        totalHits,
        totalMisses,
        cacheHitRate,
        hitRateTrend,
        totalCostSaved,
        totalResponseTimeSaved,
        averageResponseTimeSaved,
        averageCostSavedPerHit,
        totalStorageBytes,
        
        // Breakdowns
        cacheByTier: Object.entries(cacheByTier).map(([tier, count]) => ({ tier, count })),
        cacheByProvider: Object.entries(cacheByProvider).map(([provider, count]) => ({ provider, count })),
        topCachedModels,
        
        // Time series data
        cacheTimeSeriesData,
        
        // Period info
        period: `${days} days`,
        startDate: startDate.toISOString(),
        endDate: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in cache analytics:', error);
    return NextResponse.json({
      error: 'Failed to fetch cache analytics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
