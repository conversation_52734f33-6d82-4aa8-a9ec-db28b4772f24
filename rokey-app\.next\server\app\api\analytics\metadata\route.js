/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analytics/metadata/route";
exports.ids = ["app/api/analytics/metadata/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fmetadata%2Froute&page=%2Fapi%2Fanalytics%2Fmetadata%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fmetadata%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fmetadata%2Froute&page=%2Fapi%2Fanalytics%2Fmetadata%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fmetadata%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_analytics_metadata_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analytics/metadata/route.ts */ \"(rsc)/./src/app/api/analytics/metadata/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analytics/metadata/route\",\n        pathname: \"/api/analytics/metadata\",\n        filename: \"route\",\n        bundlePath: \"app/api/analytics/metadata/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\analytics\\\\metadata\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_analytics_metadata_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fmetadata%2Froute&page=%2Fapi%2Fanalytics%2Fmetadata%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fmetadata%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/analytics/metadata/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/analytics/metadata/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n        // Get authenticated user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const days = parseInt(searchParams.get('days') || '30');\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - days);\n        // Get user's custom API configurations\n        const { data: configsData, count: totalConfigs } = await supabase.from('custom_api_configs').select('id, name, routing_strategy, routing_strategy_params, browsing_enabled, created_at', {\n            count: 'exact'\n        }).eq('user_id', user.id);\n        // Get routing strategy breakdown\n        const routingStrategies = {};\n        if (configsData) {\n            configsData.forEach((config)=>{\n                const strategy = config.routing_strategy || 'none';\n                routingStrategies[strategy] = (routingStrategies[strategy] || 0) + 1;\n            });\n        }\n        const routingStrategyBreakdown = Object.entries(routingStrategies).map(([strategy, count])=>({\n                strategy: formatStrategyName(strategy),\n                count,\n                percentage: totalConfigs > 0 ? count / totalConfigs * 100 : 0\n            }));\n        // Get model usage from request logs\n        const { data: modelUsageData } = await supabase.from('request_logs').select('llm_model_name, llm_provider_name').eq('user_id', user.id).gte('request_timestamp', startDate.toISOString()).not('llm_model_name', 'is', null);\n        const modelUsage = {};\n        const providerUsage = {};\n        if (modelUsageData) {\n            modelUsageData.forEach((log)=>{\n                const model = log.llm_model_name;\n                const provider = log.llm_provider_name;\n                modelUsage[model] = (modelUsage[model] || 0) + 1;\n                if (provider) {\n                    providerUsage[provider] = (providerUsage[provider] || 0) + 1;\n                }\n            });\n        }\n        const topModels = Object.entries(modelUsage).sort(([, a], [, b])=>b - a).slice(0, 15).map(([model, count])=>({\n                model,\n                count\n            }));\n        const topProviders = Object.entries(providerUsage).sort(([, a], [, b])=>b - a).slice(0, 10).map(([provider, count])=>({\n                provider,\n                count\n            }));\n        // Get API keys metadata\n        const { data: apiKeysData, count: totalApiKeys } = await supabase.from('api_keys').select('provider, predefined_model_id, temperature, created_at', {\n            count: 'exact'\n        }).eq('user_id', user.id);\n        // Temperature distribution\n        const temperatureRanges = {\n            'Very Low (0-0.3)': 0,\n            'Low (0.3-0.5)': 0,\n            'Medium (0.5-0.7)': 0,\n            'High (0.7-0.9)': 0,\n            'Very High (0.9-1.0)': 0\n        };\n        if (apiKeysData) {\n            apiKeysData.forEach((key)=>{\n                const temp = key.temperature || 0.7;\n                if (temp < 0.3) temperatureRanges['Very Low (0-0.3)']++;\n                else if (temp < 0.5) temperatureRanges['Low (0.3-0.5)']++;\n                else if (temp < 0.7) temperatureRanges['Medium (0.5-0.7)']++;\n                else if (temp < 0.9) temperatureRanges['High (0.7-0.9)']++;\n                else temperatureRanges['Very High (0.9-1.0)']++;\n            });\n        }\n        const temperatureDistribution = Object.entries(temperatureRanges).map(([range, count])=>({\n                range,\n                count,\n                percentage: totalApiKeys > 0 ? count / totalApiKeys * 100 : 0\n            }));\n        // Get user-generated API keys metadata\n        const { data: userApiKeysData, count: totalUserApiKeys } = await supabase.from('user_generated_api_keys').select('permissions, status, total_requests, created_at', {\n            count: 'exact'\n        }).eq('user_id', user.id);\n        // User API keys status breakdown\n        const userApiKeyStatus = {};\n        if (userApiKeysData) {\n            userApiKeysData.forEach((key)=>{\n                const status = key.status || 'unknown';\n                userApiKeyStatus[status] = (userApiKeyStatus[status] || 0) + 1;\n            });\n        }\n        const userApiKeyStatusBreakdown = Object.entries(userApiKeyStatus).map(([status, count])=>({\n                status,\n                count,\n                percentage: totalUserApiKeys > 0 ? count / totalUserApiKeys * 100 : 0\n            }));\n        // Get request metadata patterns\n        const { data: requestMetadata } = await supabase.from('request_logs').select('request_payload_summary, response_payload_summary, processing_duration_ms').eq('user_id', user.id).gte('request_timestamp', startDate.toISOString()).limit(1000); // Limit for performance\n        // Analyze request patterns\n        let totalProcessingTime = 0;\n        let requestCount = 0;\n        const requestSizes = [];\n        const responseSizes = [];\n        if (requestMetadata) {\n            requestMetadata.forEach((log)=>{\n                if (log.processing_duration_ms) {\n                    totalProcessingTime += log.processing_duration_ms;\n                    requestCount++;\n                }\n                if (log.request_payload_summary) {\n                    const requestSize = JSON.stringify(log.request_payload_summary).length;\n                    requestSizes.push(requestSize);\n                }\n                if (log.response_payload_summary) {\n                    const responseSize = JSON.stringify(log.response_payload_summary).length;\n                    responseSizes.push(responseSize);\n                }\n            });\n        }\n        const averageProcessingTime = requestCount > 0 ? totalProcessingTime / requestCount : 0;\n        const averageRequestSize = requestSizes.length > 0 ? requestSizes.reduce((a, b)=>a + b, 0) / requestSizes.length : 0;\n        const averageResponseSize = responseSizes.length > 0 ? responseSizes.reduce((a, b)=>a + b, 0) / responseSizes.length : 0;\n        // Get browsing configurations\n        const browsingEnabledConfigs = configsData ? configsData.filter((config)=>config.browsing_enabled).length : 0;\n        const browsingDisabledConfigs = (totalConfigs || 0) - browsingEnabledConfigs;\n        // Get role usage from quality metrics\n        const { data: roleUsageData } = await supabase.from('routing_quality_metrics').select('routing_strategy').eq('user_id', user.id).gte('created_at', startDate.toISOString());\n        const roleStrategiesUsed = {};\n        if (roleUsageData) {\n            roleUsageData.forEach((metric)=>{\n                const strategy = metric.routing_strategy || 'unknown';\n                roleStrategiesUsed[strategy] = (roleStrategiesUsed[strategy] || 0) + 1;\n            });\n        }\n        const roleStrategyUsage = Object.entries(roleStrategiesUsed).map(([strategy, count])=>({\n                strategy: formatStrategyName(strategy),\n                count\n            })).sort((a, b)=>b.count - a.count);\n        // Configuration age analysis\n        const configAges = [];\n        if (configsData) {\n            const now = new Date();\n            configsData.forEach((config)=>{\n                const created = new Date(config.created_at);\n                const ageInDays = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24));\n                configAges.push(ageInDays);\n            });\n        }\n        const averageConfigAge = configAges.length > 0 ? configAges.reduce((a, b)=>a + b, 0) / configAges.length : 0;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                // Configuration metrics\n                totalConfigs: totalConfigs || 0,\n                totalApiKeys: totalApiKeys || 0,\n                totalUserApiKeys: totalUserApiKeys || 0,\n                averageConfigAge,\n                // Routing strategies\n                routingStrategyBreakdown,\n                roleStrategyUsage,\n                // Model and provider usage\n                topModels,\n                topProviders,\n                uniqueModels: Object.keys(modelUsage).length,\n                uniqueProviders: Object.keys(providerUsage).length,\n                // Configuration settings\n                temperatureDistribution,\n                userApiKeyStatusBreakdown,\n                // Browsing configuration\n                browsingConfiguration: {\n                    enabled: browsingEnabledConfigs,\n                    disabled: browsingDisabledConfigs,\n                    enabledPercentage: totalConfigs > 0 ? browsingEnabledConfigs / totalConfigs * 100 : 0\n                },\n                // Performance metrics\n                averageProcessingTime,\n                averageRequestSize,\n                averageResponseSize,\n                // Period info\n                period: `${days} days`,\n                startDate: startDate.toISOString(),\n                endDate: new Date().toISOString()\n            }\n        });\n    } catch (error) {\n        console.error('Error in metadata analytics:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch metadata analytics',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nfunction formatStrategyName(strategy) {\n    const strategyNames = {\n        'none': 'No Routing',\n        'intelligent_role': 'Intelligent Role',\n        'complexity_round_robin': 'Complexity Round Robin',\n        'auto_optimal': 'Auto Optimal',\n        'strict_fallback': 'Strict Fallback',\n        'cost_optimized': 'Cost Optimized',\n        'ab_routing': 'A/B Testing',\n        'unknown': 'Unknown'\n    };\n    return strategyNames[strategy] || strategy;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analytics/metadata/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fmetadata%2Froute&page=%2Fapi%2Fanalytics%2Fmetadata%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fmetadata%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();