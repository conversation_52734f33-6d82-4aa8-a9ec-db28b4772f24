'use client';

import { GlobeAltIcon } from '@heroicons/react/24/outline';
import { useEdges, useNodes } from '@xyflow/react';
import BaseNode from './BaseNode';
import { WorkflowNode, BrowsingNodeData } from '@/types/manualBuild';

const providerColors = {
  openai: '#10b981',
  anthropic: '#f97316',
  google: '#3b82f6',
  deepseek: '#8b5cf6',
  xai: '#374151',
  openrouter: 'linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)'
};

const providerNames = {
  openai: 'OpenAI',
  anthropic: 'Anthropic',
  google: 'Google',
  deepseek: 'DeepSeek',
  xai: 'xAI (Grok)',
  openrouter: 'OpenRouter'
};

interface BrowsingNodeProps {
  data: WorkflowNode['data'];
  id: string;
}

export default function BrowsingNode({ data, id }: BrowsingNodeProps) {
  const edges = useEdges();
  const nodes = useNodes();
  const config = data.config as BrowsingNodeData['config'];
  const providerId = config?.providerId;
  const modelId = config?.modelId;
  const color = providerId ? providerColors[providerId] : '#10b981'; // Green default for browsing
  const providerName = providerId ? providerNames[providerId] : 'Browsing AI';

  const maxSites = config?.maxSites || 5;
  const timeout = config?.timeout || 30;
  const enableScreenshots = config?.enableScreenshots ?? true;
  const enableFormFilling = config?.enableFormFilling ?? true;
  const searchEngines = config?.searchEngines || ['google'];

  const getCapabilities = () => {
    const capabilities = [];
    if (enableScreenshots) capabilities.push('📸 Screenshots');
    if (enableFormFilling) capabilities.push('📝 Forms');
    if (config?.enableCaptchaSolving) capabilities.push('🔐 CAPTCHAs');
    return capabilities;
  };

  return (
    <BaseNode
      data={data}
      icon={GlobeAltIcon}
      color={typeof color === 'string' ? color : '#10b981'}
      hasInput={false}
      hasOutput={true}
      inputHandles={[
        { id: 'plan', label: 'Plan', position: 'left' },
        { id: 'memory', label: 'Memory', position: 'left' }
      ]}
    >
      <div className="space-y-3">
        {providerId ? (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-white">
                {providerName}
              </span>
              <span className="text-xs bg-gradient-to-r from-green-500 to-blue-500 text-white px-2 py-0.5 rounded-full">
                Browsing
              </span>
            </div>

            {modelId && (
              <div className="text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded">
                Model: {modelId}
              </div>
            )}

            <div className="text-xs text-gray-400">
              Max sites: {maxSites} | Timeout: {timeout}s
            </div>

            <div className="text-xs text-gray-400">
              Engines: {searchEngines.join(', ')}
            </div>

            {getCapabilities().length > 0 && (
              <div className="text-xs text-gray-400">
                {getCapabilities().join(' • ')}
              </div>
            )}

            {config?.maxDepth && (
              <div className="text-xs text-gray-400">
                Max depth: {config.maxDepth} levels
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-white">
                Browsing Agent
              </span>
              <div className="text-xs text-yellow-400">
                ⚠️ Not Configured
              </div>
            </div>

            <div className="text-xs text-gray-400">
              Configure AI provider for browsing
            </div>
          </div>
        )}

        <div className="text-xs text-green-300 bg-green-900/30 px-2 py-1 rounded">
          🌐 Autonomous Agent
        </div>

        <div className="text-xs text-gray-500">
          Requires: Planner + Memory inputs
        </div>
      </div>
    </BaseNode>
  );
}
