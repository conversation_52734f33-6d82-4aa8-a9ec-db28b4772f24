import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClientFromRequest(request);
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get total requests for error rate calculation
    const { data: totalRequestsData, count: totalRequests } = await supabase
      .from('request_logs')
      .select('id', { count: 'exact' })
      .eq('user_id', user.id)
      .gte('request_timestamp', startDate.toISOString());

    // Get failed requests (status codes >= 400)
    const { data: failedRequestsData, count: failedRequests } = await supabase
      .from('request_logs')
      .select('id, status_code, error_message, error_source, request_timestamp', { count: 'exact' })
      .eq('user_id', user.id)
      .gte('request_timestamp', startDate.toISOString())
      .gte('status_code', 400);

    // Calculate error rate
    const errorRate = totalRequests > 0 ? (failedRequests / totalRequests) * 100 : 0;

    // Get error breakdown by status code
    const statusCodeBreakdown = {};
    const errorSourceBreakdown = {};
    const errorMessages = [];

    if (failedRequestsData) {
      failedRequestsData.forEach(log => {
        // Status code breakdown
        const statusCode = log.status_code;
        statusCodeBreakdown[statusCode] = (statusCodeBreakdown[statusCode] || 0) + 1;

        // Error source breakdown
        const errorSource = log.error_source || 'unknown';
        errorSourceBreakdown[errorSource] = (errorSourceBreakdown[errorSource] || 0) + 1;

        // Collect error messages
        if (log.error_message) {
          errorMessages.push({
            message: log.error_message,
            source: log.error_source,
            statusCode: log.status_code,
            timestamp: log.request_timestamp
          });
        }
      });
    }

    // Get top error status codes
    const topStatusCodes = Object.entries(statusCodeBreakdown)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([code, count]) => ({ 
        statusCode: parseInt(code), 
        count,
        description: getStatusCodeDescription(parseInt(code))
      }));

    // Get top error sources
    const topErrorSources = Object.entries(errorSourceBreakdown)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([source, count]) => ({ source, count }));

    // Get error trends over time (daily error counts)
    const dailyErrors = {};
    if (failedRequestsData) {
      failedRequestsData.forEach(log => {
        const date = new Date(log.request_timestamp).toISOString().split('T')[0];
        dailyErrors[date] = (dailyErrors[date] || 0) + 1;
      });
    }

    // Convert to time series format
    const errorTimeSeriesData = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      errorTimeSeriesData.push({
        date: dateStr,
        errors: dailyErrors[dateStr] || 0
      });
    }

    // Get recent error messages (last 20)
    const recentErrors = errorMessages
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 20);

    // Calculate error rate trend (compare with previous period)
    const previousStartDate = new Date(startDate);
    previousStartDate.setDate(previousStartDate.getDate() - days);

    const { count: previousTotalRequests } = await supabase
      .from('request_logs')
      .select('id', { count: 'exact' })
      .eq('user_id', user.id)
      .gte('request_timestamp', previousStartDate.toISOString())
      .lt('request_timestamp', startDate.toISOString());

    const { count: previousFailedRequests } = await supabase
      .from('request_logs')
      .select('id', { count: 'exact' })
      .eq('user_id', user.id)
      .gte('request_timestamp', previousStartDate.toISOString())
      .lt('request_timestamp', startDate.toISOString())
      .gte('status_code', 400);

    const previousErrorRate = previousTotalRequests > 0 ? (previousFailedRequests / previousTotalRequests) * 100 : 0;
    const errorRateTrend = errorRate - previousErrorRate;

    return NextResponse.json({
      success: true,
      data: {
        // Summary metrics
        totalRequests: totalRequests || 0,
        failedRequests: failedRequests || 0,
        errorRate,
        errorRateTrend,
        
        // Breakdowns
        statusCodeBreakdown: topStatusCodes,
        errorSourceBreakdown: topErrorSources,
        
        // Time series data
        errorTimeSeriesData,
        
        // Recent errors
        recentErrors,
        
        // Period info
        period: `${days} days`,
        startDate: startDate.toISOString(),
        endDate: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in errors analytics:', error);
    return NextResponse.json({
      error: 'Failed to fetch errors analytics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

function getStatusCodeDescription(statusCode: number): string {
  const descriptions = {
    400: 'Bad Request',
    401: 'Unauthorized',
    403: 'Forbidden',
    404: 'Not Found',
    405: 'Method Not Allowed',
    409: 'Conflict',
    422: 'Unprocessable Entity',
    429: 'Too Many Requests',
    500: 'Internal Server Error',
    502: 'Bad Gateway',
    503: 'Service Unavailable',
    504: 'Gateway Timeout'
  };
  
  return descriptions[statusCode] || 'Unknown Error';
}
