import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClientFromRequest(request);
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get all feedback data
    const { data: feedbackData, count: totalFeedback } = await supabase
      .from('routing_quality_metrics')
      .select('*', { count: 'exact' })
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });

    if (!feedbackData || feedbackData.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          totalFeedback: 0,
          averageRating: 0,
          averageQualityScore: 0,
          ratingTrend: 0,
          qualityTrend: 0,
          ratingDistribution: [],
          qualityDistribution: [],
          feedbackByProvider: [],
          feedbackByModel: [],
          behavioralSignals: {
            copiedResponses: 0,
            regeneratedResponses: 0,
            followupQuestions: 0,
            continuedConversations: 0
          },
          feedbackTimeSeriesData: [],
          taskCategoryBreakdown: [],
          complexityBreakdown: [],
          period: `${days} days`,
          startDate: startDate.toISOString(),
          endDate: new Date().toISOString()
        }
      });
    }

    // Calculate summary metrics
    const totalRating = feedbackData.reduce((sum, item) => sum + (item.user_rating || 0), 0);
    const totalQualityScore = feedbackData.reduce((sum, item) => sum + (item.quality_score || 0), 0);
    const averageRating = totalFeedback > 0 ? totalRating / totalFeedback : 0;
    const averageQualityScore = totalFeedback > 0 ? totalQualityScore / totalFeedback : 0;

    // Calculate rating distribution
    const ratingCounts = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    feedbackData.forEach(item => {
      const rating = item.user_rating;
      if (rating >= 1 && rating <= 5) {
        ratingCounts[rating]++;
      }
    });

    const ratingDistribution = Object.entries(ratingCounts).map(([rating, count]) => ({
      rating: parseInt(rating),
      count,
      percentage: totalFeedback > 0 ? (count / totalFeedback) * 100 : 0
    }));

    // Calculate quality score distribution
    const qualityRanges = {
      'Poor (0-2)': 0,
      'Fair (2-4)': 0,
      'Good (4-6)': 0,
      'Very Good (6-8)': 0,
      'Excellent (8-10)': 0
    };

    feedbackData.forEach(item => {
      const score = item.quality_score || 0;
      if (score < 2) qualityRanges['Poor (0-2)']++;
      else if (score < 4) qualityRanges['Fair (2-4)']++;
      else if (score < 6) qualityRanges['Good (4-6)']++;
      else if (score < 8) qualityRanges['Very Good (6-8)']++;
      else qualityRanges['Excellent (8-10)']++;
    });

    const qualityDistribution = Object.entries(qualityRanges).map(([range, count]) => ({
      range,
      count,
      percentage: totalFeedback > 0 ? (count / totalFeedback) * 100 : 0
    }));

    // Feedback by provider
    const providerFeedback = {};
    feedbackData.forEach(item => {
      const provider = item.provider || 'unknown';
      if (!providerFeedback[provider]) {
        providerFeedback[provider] = { count: 0, totalRating: 0, totalQuality: 0 };
      }
      providerFeedback[provider].count++;
      providerFeedback[provider].totalRating += item.user_rating || 0;
      providerFeedback[provider].totalQuality += item.quality_score || 0;
    });

    const feedbackByProvider = Object.entries(providerFeedback).map(([provider, data]) => ({
      provider,
      count: data.count,
      averageRating: data.count > 0 ? data.totalRating / data.count : 0,
      averageQuality: data.count > 0 ? data.totalQuality / data.count : 0
    })).sort((a, b) => b.count - a.count);

    // Feedback by model
    const modelFeedback = {};
    feedbackData.forEach(item => {
      const model = item.model_used || 'unknown';
      if (!modelFeedback[model]) {
        modelFeedback[model] = { count: 0, totalRating: 0, totalQuality: 0 };
      }
      modelFeedback[model].count++;
      modelFeedback[model].totalRating += item.user_rating || 0;
      modelFeedback[model].totalQuality += item.quality_score || 0;
    });

    const feedbackByModel = Object.entries(modelFeedback).map(([model, data]) => ({
      model,
      count: data.count,
      averageRating: data.count > 0 ? data.totalRating / data.count : 0,
      averageQuality: data.count > 0 ? data.totalQuality / data.count : 0
    })).sort((a, b) => b.count - a.count).slice(0, 10);

    // Behavioral signals
    const behavioralSignals = {
      copiedResponses: feedbackData.filter(item => item.user_copied_response).length,
      regeneratedResponses: feedbackData.filter(item => item.user_regenerated).length,
      followupQuestions: feedbackData.filter(item => item.user_asked_followup).length,
      continuedConversations: feedbackData.filter(item => item.conversation_continued).length
    };

    // Time series data (daily feedback)
    const dailyFeedback = {};
    feedbackData.forEach(item => {
      const date = new Date(item.created_at).toISOString().split('T')[0];
      if (!dailyFeedback[date]) {
        dailyFeedback[date] = { count: 0, totalRating: 0, totalQuality: 0 };
      }
      dailyFeedback[date].count++;
      dailyFeedback[date].totalRating += item.user_rating || 0;
      dailyFeedback[date].totalQuality += item.quality_score || 0;
    });

    const feedbackTimeSeriesData = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      const dayData = dailyFeedback[dateStr] || { count: 0, totalRating: 0, totalQuality: 0 };
      
      feedbackTimeSeriesData.push({
        date: dateStr,
        count: dayData.count,
        averageRating: dayData.count > 0 ? dayData.totalRating / dayData.count : 0,
        averageQuality: dayData.count > 0 ? dayData.totalQuality / dayData.count : 0
      });
    }

    // Task category breakdown
    const taskCategories = {};
    feedbackData.forEach(item => {
      const category = item.task_category || 'unknown';
      if (!taskCategories[category]) {
        taskCategories[category] = { count: 0, totalRating: 0, totalQuality: 0 };
      }
      taskCategories[category].count++;
      taskCategories[category].totalRating += item.user_rating || 0;
      taskCategories[category].totalQuality += item.quality_score || 0;
    });

    const taskCategoryBreakdown = Object.entries(taskCategories).map(([category, data]) => ({
      category,
      count: data.count,
      averageRating: data.count > 0 ? data.totalRating / data.count : 0,
      averageQuality: data.count > 0 ? data.totalQuality / data.count : 0
    })).sort((a, b) => b.count - a.count);

    // Complexity breakdown
    const complexityLevels = {};
    feedbackData.forEach(item => {
      const complexity = item.prompt_complexity_level || 0;
      const level = `Level ${complexity}`;
      if (!complexityLevels[level]) {
        complexityLevels[level] = { count: 0, totalRating: 0, totalQuality: 0 };
      }
      complexityLevels[level].count++;
      complexityLevels[level].totalRating += item.user_rating || 0;
      complexityLevels[level].totalQuality += item.quality_score || 0;
    });

    const complexityBreakdown = Object.entries(complexityLevels).map(([level, data]) => ({
      level,
      count: data.count,
      averageRating: data.count > 0 ? data.totalRating / data.count : 0,
      averageQuality: data.count > 0 ? data.totalQuality / data.count : 0
    })).sort((a, b) => parseInt(a.level.split(' ')[1]) - parseInt(b.level.split(' ')[1]));

    // Calculate trends (compare with previous period)
    const previousStartDate = new Date(startDate);
    previousStartDate.setDate(previousStartDate.getDate() - days);

    const { data: previousFeedbackData } = await supabase
      .from('routing_quality_metrics')
      .select('user_rating, quality_score')
      .eq('user_id', user.id)
      .gte('created_at', previousStartDate.toISOString())
      .lt('created_at', startDate.toISOString());

    let previousAverageRating = 0;
    let previousAverageQuality = 0;

    if (previousFeedbackData && previousFeedbackData.length > 0) {
      const prevTotalRating = previousFeedbackData.reduce((sum, item) => sum + (item.user_rating || 0), 0);
      const prevTotalQuality = previousFeedbackData.reduce((sum, item) => sum + (item.quality_score || 0), 0);
      previousAverageRating = prevTotalRating / previousFeedbackData.length;
      previousAverageQuality = prevTotalQuality / previousFeedbackData.length;
    }

    const ratingTrend = averageRating - previousAverageRating;
    const qualityTrend = averageQualityScore - previousAverageQuality;

    return NextResponse.json({
      success: true,
      data: {
        // Summary metrics
        totalFeedback: totalFeedback || 0,
        averageRating,
        averageQualityScore,
        ratingTrend,
        qualityTrend,
        
        // Distributions
        ratingDistribution,
        qualityDistribution,
        
        // Breakdowns
        feedbackByProvider,
        feedbackByModel,
        taskCategoryBreakdown,
        complexityBreakdown,
        
        // Behavioral data
        behavioralSignals,
        
        // Time series data
        feedbackTimeSeriesData,
        
        // Period info
        period: `${days} days`,
        startDate: startDate.toISOString(),
        endDate: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in feedback analytics:', error);
    return NextResponse.json({
      error: 'Failed to fetch feedback analytics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
