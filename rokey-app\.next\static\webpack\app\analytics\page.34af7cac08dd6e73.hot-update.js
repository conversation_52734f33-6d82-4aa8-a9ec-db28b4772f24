"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ServerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BugAntIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CreditCardIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Utility functions\nconst formatNumber = (num)=>{\n    if (num >= 1000000) return \"\".concat((num / 1000000).toFixed(1), \"M\");\n    if (num >= 1000) return \"\".concat((num / 1000).toFixed(1), \"K\");\n    return num.toString();\n};\nconst formatCurrency = (amount)=>{\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD',\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 4\n    }).format(amount);\n};\nconst calculateTrend = (current, previous)=>{\n    if (previous === 0) return {\n        percentage: 0,\n        isPositive: current > 0\n    };\n    const percentage = (current - previous) / previous * 100;\n    return {\n        percentage: Math.abs(percentage),\n        isPositive: percentage >= 0\n    };\n};\n// MetricCard Component\nconst MetricCard = (param)=>{\n    let { title, value, trend, icon, subtitle } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-400 mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: typeof value === 'number' ? formatNumber(value) : value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 11\n                                }, undefined),\n                                trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm px-2 py-1 rounded-md flex items-center space-x-1 \".concat(trend.isPositive ? 'text-green-400 bg-green-400/10' : 'text-red-400 bg-red-400/10'),\n                                    children: [\n                                        trend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                trend.percentage.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 9\n                        }, undefined),\n                        subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: subtitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 22\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 102,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 101,\n        columnNumber: 3\n    }, undefined);\n};\n_c = MetricCard;\nfunction AnalyticsPageContent() {\n    var _subscription_user, _subscription_user1, _analyticsData_grouped_data;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const subscription = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription)();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previousPeriodData, setPreviousPeriodData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeSeriesData, setTimeSeriesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Tab-specific data states\n    const [usersData, setUsersData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [errorsData, setErrorsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cacheData, setCacheData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [feedbackData, setFeedbackData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [metadataData, setMetadataData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tabLoading, setTabLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Filters\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('30');\n    const [selectedConfig, setSelectedConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            fetchCustomConfigs();\n        }\n    }[\"AnalyticsPageContent.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            var _subscription_user, _subscription_user1;\n            console.log('[Analytics] Subscription state:', {\n                subscription: !!subscription,\n                user: subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id,\n                loading: subscription === null || subscription === void 0 ? void 0 : subscription.loading,\n                error: subscription === null || subscription === void 0 ? void 0 : subscription.error,\n                isAuthenticated: subscription === null || subscription === void 0 ? void 0 : subscription.isAuthenticated\n            });\n            // If subscription hook is still loading, wait\n            if ((subscription === null || subscription === void 0 ? void 0 : subscription.loading) === true) {\n                console.log('[Analytics] Subscription still loading, waiting...');\n                return;\n            }\n            // If we have a user, fetch analytics data\n            if (subscription === null || subscription === void 0 ? void 0 : (_subscription_user1 = subscription.user) === null || _subscription_user1 === void 0 ? void 0 : _subscription_user1.id) {\n                console.log('[Analytics] User authenticated, fetching data...');\n                setError(null); // Clear any previous errors\n                fetchAnalyticsData();\n            } else if ((subscription === null || subscription === void 0 ? void 0 : subscription.loading) === false) {\n                // Only show error if subscription has finished loading and no user\n                console.log('[Analytics] No user found after loading completed');\n                setError('Authentication required. Please log in to view analytics.');\n                setLoading(false);\n            }\n        // If subscription is undefined or loading is undefined, keep waiting\n        }\n    }[\"AnalyticsPageContent.useEffect\"], [\n        subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id,\n        subscription === null || subscription === void 0 ? void 0 : subscription.loading,\n        timeRange,\n        selectedConfig\n    ]);\n    const fetchCustomConfigs = async ()=>{\n        try {\n            const response = await fetch('/api/custom-configs');\n            if (response.ok) {\n                const configs = await response.json();\n                setCustomConfigs(configs);\n            }\n        } catch (err) {\n            console.error('Error fetching configs:', err);\n        }\n    };\n    const fetchAnalyticsData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnalyticsPageContent.useCallback[fetchAnalyticsData]\": async ()=>{\n            try {\n                var _subscription_user, _data_grouped_data, _data_summary, _data_summary1;\n                setLoading(true);\n                setError(null);\n                console.log('[Analytics] Starting data fetch for user:', subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id);\n                // First, try a simple test to see if the API is accessible\n                try {\n                    const testResponse = await fetch('/api/analytics/summary?groupBy=provider');\n                    console.log('[Analytics] Test API response status:', testResponse.status);\n                    if (testResponse.status === 401) {\n                        throw new Error('Authentication required. Please log in to view analytics.');\n                    }\n                } catch (testErr) {\n                    console.error('[Analytics] Test API failed:', testErr);\n                }\n                // Build query parameters\n                const params = new URLSearchParams();\n                const startDate = new Date();\n                startDate.setDate(startDate.getDate() - parseInt(timeRange));\n                params.append('startDate', startDate.toISOString());\n                if (selectedConfig) {\n                    params.append('customApiConfigId', selectedConfig);\n                }\n                console.log('[Analytics] Fetching with params:', params.toString());\n                // Fetch just the main analytics data first\n                const response = await fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=provider\"));\n                console.log('[Analytics] Response status:', response.status);\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    console.error('[Analytics] API Error:', errorText);\n                    if (response.status === 401) {\n                        throw new Error('Authentication required. Please log in to view analytics.');\n                    }\n                    throw new Error(\"Failed to fetch analytics data: \".concat(response.status, \" \").concat(errorText));\n                }\n                const data = await response.json();\n                console.log('[Analytics] Received data:', {\n                    summary: data.summary,\n                    groupedDataCount: ((_data_grouped_data = data.grouped_data) === null || _data_grouped_data === void 0 ? void 0 : _data_grouped_data.length) || 0,\n                    hasData: ((_data_summary = data.summary) === null || _data_summary === void 0 ? void 0 : _data_summary.total_requests) > 0\n                });\n                setAnalyticsData(data);\n                // Only fetch additional data if we have some basic data\n                if (((_data_summary1 = data.summary) === null || _data_summary1 === void 0 ? void 0 : _data_summary1.total_requests) > 0) {\n                    try {\n                        // Build previous period params for comparison\n                        const prevParams = new URLSearchParams();\n                        const prevStartDate = new Date();\n                        prevStartDate.setDate(prevStartDate.getDate() - parseInt(timeRange) * 2);\n                        const prevEndDate = new Date();\n                        prevEndDate.setDate(prevEndDate.getDate() - parseInt(timeRange));\n                        prevParams.append('startDate', prevStartDate.toISOString());\n                        prevParams.append('endDate', prevEndDate.toISOString());\n                        if (selectedConfig) {\n                            prevParams.append('customApiConfigId', selectedConfig);\n                        }\n                        // Fetch additional data\n                        const [prevResponse, timeSeriesResponse] = await Promise.all([\n                            fetch(\"/api/analytics/summary?\".concat(prevParams.toString(), \"&groupBy=provider\")),\n                            fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=day\"))\n                        ]);\n                        const prevData = prevResponse.ok ? await prevResponse.json() : null;\n                        const timeSeriesData = timeSeriesResponse.ok ? await timeSeriesResponse.json() : null;\n                        setPreviousPeriodData(prevData);\n                        if (timeSeriesData === null || timeSeriesData === void 0 ? void 0 : timeSeriesData.grouped_data) {\n                            const formattedTimeSeries = timeSeriesData.grouped_data.map({\n                                \"AnalyticsPageContent.useCallback[fetchAnalyticsData].formattedTimeSeries\": (item)=>({\n                                        date: item.period || item.name,\n                                        cost: item.cost || 0,\n                                        requests: item.requests || 0,\n                                        tokens: (item.input_tokens || 0) + (item.output_tokens || 0),\n                                        latency: item.avg_latency || 0\n                                    })\n                            }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData].formattedTimeSeries\"]);\n                            setTimeSeriesData(formattedTimeSeries);\n                            console.log('[Analytics] Formatted time series:', formattedTimeSeries.length, 'items');\n                        }\n                    } catch (additionalErr) {\n                        console.warn('[Analytics] Failed to fetch additional data:', additionalErr);\n                    // Don't fail the whole request if additional data fails\n                    }\n                }\n            } catch (err) {\n                console.error('[Analytics] Error fetching data:', err);\n                setError(err.message);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData]\"], [\n        timeRange,\n        selectedConfig\n    ]);\n    // Loading state - show loading if subscription is loading OR analytics data is loading\n    if ((subscription === null || subscription === void 0 ? void 0 : subscription.loading) !== false || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen w-full bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-8 bg-gray-800 rounded w-1/3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    ...Array(4)\n                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 rounded-lg p-6 border border-gray-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-800 rounded w-1/2 mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 bg-gray-800 rounded w-3/4 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-gray-800 rounded w-1/3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, i, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-4 right-4 bg-gray-800 rounded-lg p-3 flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-300\",\n                                children: (subscription === null || subscription === void 0 ? void 0 : subscription.loading) !== false ? 'Authenticating...' : 'Loading analytics...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 312,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen w-full bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Analytics\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900/20 border border-red-800 rounded-lg p-6 max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-400 mb-4\",\n                                    children: [\n                                        \"Error loading analytics: \",\n                                        error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchAnalyticsData,\n                                    className: \"px-4 py-2 bg-cyan-500 text-white rounded hover:bg-cyan-600 transition-colors\",\n                                    children: \"Retry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 342,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 341,\n            columnNumber: 7\n        }, this);\n    }\n    const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n    const previousSummary = previousPeriodData === null || previousPeriodData === void 0 ? void 0 : previousPeriodData.summary;\n    // Calculate trends\n    const costTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0, previousSummary.total_cost) : undefined;\n    const requestTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0, previousSummary.total_requests) : undefined;\n    const latencyTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0, previousSummary.average_latency || 0) : undefined;\n    const successTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0, previousSummary.success_rate) : undefined;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-semibold text-white\",\n                                    children: \"Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search Filter\",\n                                                className: \"bg-transparent border-none outline-none text-gray-400 placeholder-gray-500 w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: timeRange,\n                                        onChange: (e)=>setTimeRange(e.target.value),\n                                        className: \"px-3 py-1 bg-gray-800 border border-gray-700 rounded text-sm text-white focus:outline-none focus:border-cyan-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"7\",\n                                                children: \"Last 7 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"30\",\n                                                children: \"Last 30 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"90\",\n                                                children: \"Last 90 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('overview'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'overview' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('users'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'users' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Users\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('errors'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'errors' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Errors\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('cache'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'cache' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Cache\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('feedback'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'feedback' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Feedback\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('metadata'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'metadata' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Metadata\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 402,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Total Request Made\",\n                                        value: (summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0,\n                                        trend: requestTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Average Latency\",\n                                        value: \"\".concat(Math.round((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0), \"ms\"),\n                                        trend: latencyTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"User Feedback\",\n                                        value: \"\".concat(((summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0).toFixed(1), \"%\"),\n                                        trend: successTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Total Cost\",\n                                        value: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0),\n                                        trend: costTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 13\n                            }, this),\n                            (!summary || summary.total_requests === 0) && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-16 w-16 mx-auto mb-4 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-2\",\n                                        children: \"No Analytics Data Yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"Start making API requests to see your analytics data here.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mb-6\",\n                                        children: \"Analytics will appear once you begin using your API configurations.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 17\n                                    }, this),\n                                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/50 rounded-lg p-4 text-left max-w-md mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-semibold text-white mb-2\",\n                                                children: \"Debug Info:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"User ID: \",\n                                                            (subscription === null || subscription === void 0 ? void 0 : (_subscription_user1 = subscription.user) === null || _subscription_user1 === void 0 ? void 0 : _subscription_user1.id) || 'Not logged in'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Time Range: \",\n                                                            timeRange,\n                                                            \" days\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Selected Config: \",\n                                                            selectedConfig || 'All configs'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Loading: \",\n                                                            loading ? 'Yes' : 'No'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Error: \",\n                                                            error || 'None'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    console.log('[Analytics] Manual refresh triggered');\n                                                    fetchAnalyticsData();\n                                                },\n                                                className: \"mt-3 px-3 py-1 bg-cyan-500 text-white text-xs rounded hover:bg-cyan-600 transition-colors\",\n                                                children: \"Refresh Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Cost\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-3xl font-bold text-white\",\n                                                                        children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    costTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm px-2 py-1 rounded flex items-center space-x-1 \".concat(costTrend.isPositive ? 'text-red-400 bg-red-400/10' : 'text-green-400 bg-green-400/10'),\n                                                                        children: [\n                                                                            costTrend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 567,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 569,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    costTrend.percentage.toFixed(1),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 571,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-48 relative bg-gray-800/50 rounded\",\n                                                children: timeSeriesData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 400 120\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"costGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#10b981\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 588,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#10b981\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 587,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            [\n                                                                ...Array(5)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                    x1: \"0\",\n                                                                    y1: i * 24,\n                                                                    x2: \"400\",\n                                                                    y2: i * 24,\n                                                                    stroke: \"#374151\",\n                                                                    strokeWidth: \"0.5\",\n                                                                    opacity: \"0.3\"\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 595,\n                                                                    columnNumber: 27\n                                                                }, this)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                fill: \"none\",\n                                                                stroke: \"#10b981\",\n                                                                strokeWidth: \"2\",\n                                                                points: timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxCost = Math.max(...timeSeriesData.map((item)=>item.cost));\n                                                                    const y = 120 - d.cost / maxCost * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' ')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                fill: \"url(#costGradient)\",\n                                                                points: \"0,120 \".concat(timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxCost = Math.max(...timeSeriesData.map((item)=>item.cost));\n                                                                    const y = 120 - d.cost / maxCost * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' '), \" 400,120\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-8 w-8 mx-auto mb-2 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"No data available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Latency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 647,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-3xl font-bold text-white\",\n                                                                        children: [\n                                                                            Math.round((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0),\n                                                                            \"ms\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 649,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    latencyTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm px-2 py-1 rounded flex items-center space-x-1 \".concat(latencyTrend.isPositive ? 'text-red-400 bg-red-400/10' : 'text-green-400 bg-green-400/10'),\n                                                                        children: [\n                                                                            latencyTrend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 657,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 659,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    latencyTrend.percentage.toFixed(1),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 661,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 653,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-48 relative bg-gray-800/50 rounded\",\n                                                children: timeSeriesData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 400 120\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"latencyGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#f59e0b\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 678,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#f59e0b\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 679,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 676,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            [\n                                                                ...Array(5)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                    x1: \"0\",\n                                                                    y1: i * 24,\n                                                                    x2: \"400\",\n                                                                    y2: i * 24,\n                                                                    stroke: \"#374151\",\n                                                                    strokeWidth: \"0.5\",\n                                                                    opacity: \"0.3\"\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 27\n                                                                }, this)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                fill: \"none\",\n                                                                stroke: \"#f59e0b\",\n                                                                strokeWidth: \"2\",\n                                                                points: timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxLatency = Math.max(...timeSeriesData.map((item)=>item.latency || 0));\n                                                                    const y = 120 - (d.latency || 0) / Math.max(maxLatency, 1) * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' ')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 698,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                fill: \"url(#latencyGradient)\",\n                                                                points: \"0,120 \".concat(timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxLatency = Math.max(...timeSeriesData.map((item)=>item.latency || 0));\n                                                                    const y = 120 - (d.latency || 0) / Math.max(maxLatency, 1) * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' '), \" 400,120\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 711,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-8 w-8 mx-auto mb-2 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"No data available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Tokens Used\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400 mt-1\",\n                                                                children: \"March 28\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 744,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.total_tokens) || 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 752,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"8.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 753,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-32 relative bg-gray-800/50 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 mb-4 text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-yellow-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 763,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Input Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 764,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 762,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 767,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Output Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 768,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 771,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Total Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 772,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 770,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-16\",\n                                                            children: [\n                                                                ...Array(20)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute w-1 h-1 rounded-full \".concat(i % 3 === 0 ? 'bg-yellow-500' : i % 3 === 1 ? 'bg-green-500' : 'bg-blue-500'),\n                                                                    style: {\n                                                                        left: \"\".concat(Math.random() * 90, \"%\"),\n                                                                        top: \"\".concat(Math.random() * 80, \"%\")\n                                                                    }\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 779,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 777,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 799,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 798,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 811,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"3.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 812,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 810,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 806,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data.slice(0, 5).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 text-sm text-gray-400 truncate\",\n                                                                children: provider.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 820,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 mx-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-2 bg-gray-800 rounded-full overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full rounded-full \".concat(index === 0 ? 'bg-pink-500' : index === 1 ? 'bg-purple-500' : index === 2 ? 'bg-cyan-500' : index === 3 ? 'bg-green-500' : 'bg-yellow-500'),\n                                                                        style: {\n                                                                            width: \"\".concat(provider.requests / ((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 1) * 100, \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 825,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 823,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400 w-12 text-right\",\n                                                                children: formatNumber(provider.requests)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 838,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, provider.name, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 817,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 797,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Unique Users\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 849,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 848,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: (summary === null || summary === void 0 ? void 0 : summary.successful_requests) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 861,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"3.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 862,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 860,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 856,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-32 relative bg-gray-800/50 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 200 80\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"waveGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#8b5cf6\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 872,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#8b5cf6\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 873,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 871,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 870,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0,40 Q50,20 100,40 T200,40\",\n                                                                fill: \"none\",\n                                                                stroke: \"#8b5cf6\",\n                                                                strokeWidth: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 878,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0,40 Q50,20 100,40 T200,40 L200,80 L0,80 Z\",\n                                                                fill: \"url(#waveGradient)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 886,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 867,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 847,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 735,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 478,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'users' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Active Users\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 904,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-6 h-6 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 905,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 903,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: (summary === null || summary === void 0 ? void 0 : summary.unique_users) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 907,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Users with API activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 910,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 902,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"API Keys Generated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-6 h-6 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 914,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.total_api_keys) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 918,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"User-generated API keys\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Subscription Tiers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 926,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-6 h-6 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 925,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Free\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 931,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 932,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 930,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Paid\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 935,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 936,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 929,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 924,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 901,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white mb-4\",\n                                        children: \"User Activity Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 943,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Detailed user analytics and activity patterns will be displayed here based on your user_profiles, user_generated_api_keys, and request_logs data.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 942,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 900,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'errors' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Error Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 955,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-6 h-6 text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 956,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 954,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: (summary === null || summary === void 0 ? void 0 : summary.error_rate) ? \"\".concat(summary.error_rate.toFixed(2), \"%\") : '0%'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 958,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Failed requests percentage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 961,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 953,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Total Errors\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 966,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-6 h-6 text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 967,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 965,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.failed_requests) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 969,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Failed requests count\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 972,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Error Sources\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 977,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-6 h-6 text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 978,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 976,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"API Errors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 982,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 981,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Workflow Errors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 986,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 987,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 975,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 952,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white mb-4\",\n                                        children: \"Error Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 994,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Detailed error tracking and analysis will be displayed here based on your request_logs error_message and error_source data.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 995,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 993,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 951,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'cache' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Cache Hit Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1006,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1007,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1005,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: (summary === null || summary === void 0 ? void 0 : summary.cache_hit_rate) ? \"\".concat(summary.cache_hit_rate.toFixed(1), \"%\") : '0%'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1009,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Semantic cache efficiency\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1012,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1004,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Cache Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1017,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-6 h-6 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1016,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.cache_entries) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1020,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Stored cache responses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1023,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1015,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Cost Savings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1027,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.cache_savings) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Saved through caching\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1034,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1026,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 1003,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white mb-4\",\n                                        children: \"Cache Performance\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1039,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Detailed cache analytics including semantic cache hits, response cache performance, and cost optimization metrics will be displayed here.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1040,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 1038,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 1002,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'feedback' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Average Rating\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1051,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-6 h-6 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1052,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1050,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: (summary === null || summary === void 0 ? void 0 : summary.avg_rating) ? summary.avg_rating.toFixed(1) : '0.0'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1054,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"User satisfaction score\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1057,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1049,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Quality Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1062,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-6 h-6 text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1063,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1061,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: (summary === null || summary === void 0 ? void 0 : summary.avg_quality_score) ? summary.avg_quality_score.toFixed(1) : '0.0'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1065,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Response quality metric\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1068,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1060,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Feedback Count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1073,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-6 h-6 text-cyan-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1074,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1072,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.feedback_count) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1076,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Total feedback received\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1079,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1071,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 1048,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white mb-4\",\n                                        children: \"Feedback Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1084,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Detailed feedback analytics including user ratings, quality scores, behavioral signals, and A/B testing results from your routing_quality_metrics data.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1085,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 1083,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 1047,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'metadata' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Routing Strategies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1096,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-6 h-6 text-indigo-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1097,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1095,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Intelligent Role\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1101,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1102,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1100,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Cost Optimized\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1105,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1106,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1104,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1099,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1094,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Model Usage\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1113,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1114,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: (analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData_grouped_data = analyticsData.grouped_data) === null || _analyticsData_grouped_data === void 0 ? void 0 : _analyticsData_grouped_data.length) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Unique models used\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1119,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1111,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Configurations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1124,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CreditCardIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"w-6 h-6 text-orange-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1125,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1123,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: (summary === null || summary === void 0 ? void 0 : summary.total_configs) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Active API configurations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1130,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1122,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 1093,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white mb-4\",\n                                        children: \"System Metadata\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Comprehensive metadata analysis including request/response metadata, routing information, model parameters, and configuration details from your JSONB metadata fields.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1136,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 1134,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 1092,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 476,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 370,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPageContent, \"9J0lZY2P1zD1nfv5twjMs2V/88I=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription\n    ];\n});\n_c1 = AnalyticsPageContent;\nfunction AnalyticsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-800 rounded w-1/3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1151,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                ...Array(4)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-lg p-6 border border-gray-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-800 rounded w-1/2 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1155,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-800 rounded w-3/4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1156,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-800 rounded w-1/3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1157,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1154,\n                                    columnNumber: 17\n                                }, void 0))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1152,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1150,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 1149,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 1148,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 1165,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 1147,\n        columnNumber: 5\n    }, this);\n}\n_c2 = AnalyticsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MetricCard\");\n$RefreshReg$(_c1, \"AnalyticsPageContent\");\n$RefreshReg$(_c2, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

});