'use client';

import { useState, useEffect, useCallback, Suspense } from 'react';
import { useRouter } from 'next/navigation';
import { useSubscription } from '@/hooks/useSubscription';
import {
  MagnifyingGlassIcon,
  ChartBarIcon,
  UsersIcon,
  ExclamationTriangleIcon,
  ServerIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  EyeIcon,
  CalendarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ClockIcon,
  CurrencyDollarIcon,
  CpuChipIcon,
  CheckCircleIcon,
  BoltIcon,
  GlobeAltIcon,
  KeyIcon,
  CreditCardIcon,
  XCircleIcon,
  BugAntIcon,
  CircleStackIcon,
  StarIcon,
  CogIcon
} from '@heroicons/react/24/outline';

interface AnalyticsSummary {
  total_requests: number;
  successful_requests: number;
  success_rate: number;
  total_cost: number;
  total_input_tokens: number;
  total_output_tokens: number;
  total_tokens: number;
  average_cost_per_request: number;
  average_latency?: number;
}

interface AnalyticsData {
  summary: AnalyticsSummary;
  grouped_data: any[];
  filters: any;
}

interface TimeSeriesData {
  date: string;
  cost: number;
  requests: number;
  tokens: number;
  latency?: number;
}

interface CustomApiConfig {
  id: string;
  name: string;
}

interface TrendData {
  percentage: number;
  isPositive: boolean;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  trend?: TrendData;
  icon: React.ReactNode;
  subtitle?: string;
}

// Utility functions
const formatNumber = (num: number): string => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
  return num.toString();
};

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 4
  }).format(amount);
};

const calculateTrend = (current: number, previous: number): TrendData => {
  if (previous === 0) return { percentage: 0, isPositive: current > 0 };
  const percentage = ((current - previous) / previous) * 100;
  return { percentage: Math.abs(percentage), isPositive: percentage >= 0 };
};

// MetricCard Component
const MetricCard: React.FC<MetricCardProps> = ({ title, value, trend, icon, subtitle }) => (
  <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200">
    <div className="flex items-center justify-between mb-4">
      <div>
        <p className="text-sm text-gray-400 mb-1">{title}</p>
        <div className="flex items-center space-x-3">
          <span className="text-3xl font-bold text-white">
            {typeof value === 'number' ? formatNumber(value) : value}
          </span>
          {trend && (
            <span className={`text-sm px-2 py-1 rounded-md flex items-center space-x-1 ${
              trend.isPositive ? 'text-green-400 bg-green-400/10' : 'text-red-400 bg-red-400/10'
            }`}>
              {trend.isPositive ? (
                <ArrowTrendingUpIcon className="w-3 h-3" />
              ) : (
                <ArrowTrendingDownIcon className="w-3 h-3" />
              )}
              <span>{trend.percentage.toFixed(1)}%</span>
            </span>
          )}
        </div>
        {subtitle && <p className="text-xs text-gray-500 mt-1">{subtitle}</p>}
      </div>
      <div className="text-gray-500">
        {icon}
      </div>
    </div>
  </div>
);

function AnalyticsPageContent() {
  const router = useRouter();
  const subscription = useSubscription();
  
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [previousPeriodData, setPreviousPeriodData] = useState<AnalyticsData | null>(null);
  const [timeSeriesData, setTimeSeriesData] = useState<TimeSeriesData[]>([]);
  const [customConfigs, setCustomConfigs] = useState<CustomApiConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filters
  const [timeRange, setTimeRange] = useState('30');
  const [selectedConfig, setSelectedConfig] = useState<string>('');
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchCustomConfigs();
  }, []);

  useEffect(() => {
    console.log('[Analytics] Subscription state:', {
      subscription: !!subscription,
      user: subscription?.user?.id,
      loading: subscription?.loading,
      error: subscription?.error,
      isAuthenticated: subscription?.isAuthenticated
    });

    // If subscription hook is still loading, wait
    if (subscription?.loading === true) {
      console.log('[Analytics] Subscription still loading, waiting...');
      return;
    }

    // If we have a user, fetch analytics data
    if (subscription?.user?.id) {
      console.log('[Analytics] User authenticated, fetching data...');
      setError(null); // Clear any previous errors
      fetchAnalyticsData();
    } else if (subscription?.loading === false) {
      // Only show error if subscription has finished loading and no user
      console.log('[Analytics] No user found after loading completed');
      setError('Authentication required. Please log in to view analytics.');
      setLoading(false);
    }
    // If subscription is undefined or loading is undefined, keep waiting
  }, [subscription?.user?.id, subscription?.loading, timeRange, selectedConfig]);

  const fetchCustomConfigs = async () => {
    try {
      const response = await fetch('/api/custom-configs');
      if (response.ok) {
        const configs = await response.json();
        setCustomConfigs(configs);
      }
    } catch (err) {
      console.error('Error fetching configs:', err);
    }
  };

  const fetchAnalyticsData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('[Analytics] Starting data fetch for user:', subscription?.user?.id);

      // First, try a simple test to see if the API is accessible
      try {
        const testResponse = await fetch('/api/analytics/summary?groupBy=provider');
        console.log('[Analytics] Test API response status:', testResponse.status);

        if (testResponse.status === 401) {
          throw new Error('Authentication required. Please log in to view analytics.');
        }
      } catch (testErr) {
        console.error('[Analytics] Test API failed:', testErr);
      }

      // Build query parameters
      const params = new URLSearchParams();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(timeRange));
      params.append('startDate', startDate.toISOString());

      if (selectedConfig) {
        params.append('customApiConfigId', selectedConfig);
      }

      console.log('[Analytics] Fetching with params:', params.toString());

      // Fetch just the main analytics data first
      const response = await fetch(`/api/analytics/summary?${params.toString()}&groupBy=provider`);

      console.log('[Analytics] Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[Analytics] API Error:', errorText);

        if (response.status === 401) {
          throw new Error('Authentication required. Please log in to view analytics.');
        }

        throw new Error(`Failed to fetch analytics data: ${response.status} ${errorText}`);
      }

      const data = await response.json();

      console.log('[Analytics] Received data:', {
        summary: data.summary,
        groupedDataCount: data.grouped_data?.length || 0,
        hasData: data.summary?.total_requests > 0
      });

      setAnalyticsData(data);

      // Only fetch additional data if we have some basic data
      if (data.summary?.total_requests > 0) {
        try {
          // Build previous period params for comparison
          const prevParams = new URLSearchParams();
          const prevStartDate = new Date();
          prevStartDate.setDate(prevStartDate.getDate() - parseInt(timeRange) * 2);
          const prevEndDate = new Date();
          prevEndDate.setDate(prevEndDate.getDate() - parseInt(timeRange));
          prevParams.append('startDate', prevStartDate.toISOString());
          prevParams.append('endDate', prevEndDate.toISOString());

          if (selectedConfig) {
            prevParams.append('customApiConfigId', selectedConfig);
          }

          // Fetch additional data
          const [prevResponse, timeSeriesResponse] = await Promise.all([
            fetch(`/api/analytics/summary?${prevParams.toString()}&groupBy=provider`),
            fetch(`/api/analytics/summary?${params.toString()}&groupBy=day`)
          ]);

          const prevData = prevResponse.ok ? await prevResponse.json() : null;
          const timeSeriesData = timeSeriesResponse.ok ? await timeSeriesResponse.json() : null;

          setPreviousPeriodData(prevData);

          if (timeSeriesData?.grouped_data) {
            const formattedTimeSeries = timeSeriesData.grouped_data.map((item: any) => ({
              date: item.period || item.name,
              cost: item.cost || 0,
              requests: item.requests || 0,
              tokens: (item.input_tokens || 0) + (item.output_tokens || 0),
              latency: item.avg_latency || 0
            }));
            setTimeSeriesData(formattedTimeSeries);
            console.log('[Analytics] Formatted time series:', formattedTimeSeries.length, 'items');
          }
        } catch (additionalErr) {
          console.warn('[Analytics] Failed to fetch additional data:', additionalErr);
          // Don't fail the whole request if additional data fails
        }
      }

    } catch (err: any) {
      console.error('[Analytics] Error fetching data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [timeRange, selectedConfig]);

  // Loading state - show loading if subscription is loading OR analytics data is loading
  if (subscription?.loading !== false || loading) {
    return (
      <div className="min-h-screen w-full bg-[#040716] text-white">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-8">
            <div className="h-8 bg-gray-800 rounded w-1/3"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-gray-900 rounded-lg p-6 border border-gray-800">
                  <div className="h-4 bg-gray-800 rounded w-1/2 mb-4"></div>
                  <div className="h-8 bg-gray-800 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-800 rounded w-1/3"></div>
                </div>
              ))}
            </div>
          </div>
          {/* Loading indicator */}
          <div className="fixed bottom-4 right-4 bg-gray-800 rounded-lg p-3 flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-500"></div>
            <span className="text-sm text-gray-300">
              {subscription?.loading !== false ? 'Authenticating...' : 'Loading analytics...'}
            </span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen w-full bg-[#040716] text-white">
        <div className="w-full px-6 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-semibold mb-4">Analytics</h1>
            <div className="bg-red-900/20 border border-red-800 rounded-lg p-6 max-w-md mx-auto">
              <p className="text-red-400 mb-4">Error loading analytics: {error}</p>
              <button
                onClick={fetchAnalyticsData}
                className="px-4 py-2 bg-cyan-500 text-white rounded hover:bg-cyan-600 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const summary = analyticsData?.summary;
  const previousSummary = previousPeriodData?.summary;

  // Calculate trends
  const costTrend = previousSummary ? calculateTrend(summary?.total_cost || 0, previousSummary.total_cost) : undefined;
  const requestTrend = previousSummary ? calculateTrend(summary?.total_requests || 0, previousSummary.total_requests) : undefined;
  const latencyTrend = previousSummary ? calculateTrend(summary?.average_latency || 0, previousSummary.average_latency || 0) : undefined;
  const successTrend = previousSummary ? calculateTrend(summary?.success_rate || 0, previousSummary.success_rate) : undefined;

  return (
    <div className="min-h-screen w-full bg-[#040716] text-white overflow-x-hidden">
      {/* Header Section - Following reference design */}
      <div className="border-b border-gray-800/50">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <h1 className="text-2xl font-semibold text-white">Analytics</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <MagnifyingGlassIcon className="w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search Filter"
                  className="bg-transparent border-none outline-none text-gray-400 placeholder-gray-500 w-32"
                />
              </div>
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="px-3 py-1 bg-gray-800 border border-gray-700 rounded text-sm text-white focus:outline-none focus:border-cyan-500"
              >
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs - Following reference design */}
      <div className="border-b border-gray-800/50">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-8">
            <button
              onClick={() => setActiveTab('overview')}
              className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                activeTab === 'overview'
                  ? 'border-cyan-500 text-cyan-500'
                  : 'border-transparent text-gray-400 hover:text-white'
              }`}
            >
              <EyeIcon className="w-4 h-4" />
              <span className="text-sm font-medium">Overview</span>
            </button>
            <button
              onClick={() => setActiveTab('users')}
              className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                activeTab === 'users'
                  ? 'border-cyan-500 text-cyan-500'
                  : 'border-transparent text-gray-400 hover:text-white'
              }`}
            >
              <UsersIcon className="w-4 h-4" />
              <span className="text-sm">Users</span>
            </button>
            <button
              onClick={() => setActiveTab('errors')}
              className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                activeTab === 'errors'
                  ? 'border-cyan-500 text-cyan-500'
                  : 'border-transparent text-gray-400 hover:text-white'
              }`}
            >
              <ExclamationTriangleIcon className="w-4 h-4" />
              <span className="text-sm">Errors</span>
            </button>
            <button
              onClick={() => setActiveTab('cache')}
              className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                activeTab === 'cache'
                  ? 'border-cyan-500 text-cyan-500'
                  : 'border-transparent text-gray-400 hover:text-white'
              }`}
            >
              <ServerIcon className="w-4 h-4" />
              <span className="text-sm">Cache</span>
            </button>
            <button
              onClick={() => setActiveTab('feedback')}
              className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                activeTab === 'feedback'
                  ? 'border-cyan-500 text-cyan-500'
                  : 'border-transparent text-gray-400 hover:text-white'
              }`}
            >
              <ChatBubbleLeftRightIcon className="w-4 h-4" />
              <span className="text-sm">Feedback</span>
            </button>
            <button
              onClick={() => setActiveTab('metadata')}
              className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                activeTab === 'metadata'
                  ? 'border-cyan-500 text-cyan-500'
                  : 'border-transparent text-gray-400 hover:text-white'
              }`}
            >
              <DocumentTextIcon className="w-4 h-4" />
              <span className="text-sm">Metadata</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Main Metrics Grid - Following reference design */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Total Requests Made */}
              <MetricCard
                title="Total Request Made"
                value={summary?.total_requests || 0}
                trend={requestTrend}
                icon={<ChartBarIcon className="w-6 h-6" />}
              />

              {/* Average Latency */}
              <MetricCard
                title="Average Latency"
                value={`${Math.round(summary?.average_latency || 0)}ms`}
                trend={latencyTrend}
                icon={<ClockIcon className="w-6 h-6" />}
              />

              {/* User Feedback */}
              <MetricCard
                title="User Feedback"
                value={`${(summary?.success_rate || 0).toFixed(1)}%`}
                trend={successTrend}
                icon={<ChatBubbleLeftRightIcon className="w-6 h-6" />}
              />

              {/* Total Cost */}
              <MetricCard
                title="Total Cost"
                value={formatCurrency(summary?.total_cost || 0)}
                trend={costTrend}
                icon={<CurrencyDollarIcon className="w-6 h-6" />}
              />
            </div>

            {/* Show empty state if no data */}
            {(!summary || summary.total_requests === 0) && !loading && (
              <div className="text-center py-12">
                <ChartBarIcon className="h-16 w-16 mx-auto mb-4 text-gray-500" />
                <h3 className="text-xl font-semibold text-white mb-2">No Analytics Data Yet</h3>
                <p className="text-gray-400 mb-4">
                  Start making API requests to see your analytics data here.
                </p>
                <p className="text-sm text-gray-500 mb-6">
                  Analytics will appear once you begin using your API configurations.
                </p>

                {/* Debug info for development */}
                {process.env.NODE_ENV === 'development' && (
                  <div className="bg-gray-800/50 rounded-lg p-4 text-left max-w-md mx-auto">
                    <h4 className="text-sm font-semibold text-white mb-2">Debug Info:</h4>
                    <div className="text-xs text-gray-400 space-y-1">
                      <p>User ID: {subscription?.user?.id || 'Not logged in'}</p>
                      <p>Time Range: {timeRange} days</p>
                      <p>Selected Config: {selectedConfig || 'All configs'}</p>
                      <p>Loading: {loading ? 'Yes' : 'No'}</p>
                      <p>Error: {error || 'None'}</p>
                    </div>
                    <button
                      onClick={() => {
                        console.log('[Analytics] Manual refresh triggered');
                        fetchAnalyticsData();
                      }}
                      className="mt-3 px-3 py-1 bg-cyan-500 text-white text-xs rounded hover:bg-cyan-600 transition-colors"
                    >
                      Refresh Data
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Charts Section - Following reference layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Cost Chart */}
              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h3 className="text-lg font-semibold text-white">Cost</h3>
                    <div className="flex items-center space-x-2 mt-2">
                      <span className="text-3xl font-bold text-white">
                        {formatCurrency(summary?.total_cost || 0)}
                      </span>
                      {costTrend && (
                        <span className={`text-sm px-2 py-1 rounded flex items-center space-x-1 ${
                          costTrend.isPositive ? 'text-red-400 bg-red-400/10' : 'text-green-400 bg-green-400/10'
                        }`}>
                          {costTrend.isPositive ? (
                            <ArrowTrendingUpIcon className="w-3 h-3" />
                          ) : (
                            <ArrowTrendingDownIcon className="w-3 h-3" />
                          )}
                          <span>{costTrend.percentage.toFixed(1)}%</span>
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="text-gray-500">
                    <CurrencyDollarIcon className="w-6 h-6" />
                  </div>
                </div>

                {/* Cost Chart Visualization */}
                <div className="h-48 relative bg-gray-800/50 rounded">
                  {timeSeriesData.length > 0 ? (
                    <div className="absolute inset-4">
                      <svg className="w-full h-full" viewBox="0 0 400 120">
                        <defs>
                          <linearGradient id="costGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" stopColor="#10b981" stopOpacity="0.3" />
                            <stop offset="100%" stopColor="#10b981" stopOpacity="0" />
                          </linearGradient>
                        </defs>

                        {/* Grid lines */}
                        {[...Array(5)].map((_, i) => (
                          <line
                            key={i}
                            x1="0"
                            y1={i * 24}
                            x2="400"
                            y2={i * 24}
                            stroke="#374151"
                            strokeWidth="0.5"
                            opacity="0.3"
                          />
                        ))}

                        {/* Cost line */}
                        <polyline
                          fill="none"
                          stroke="#10b981"
                          strokeWidth="2"
                          points={timeSeriesData.map((d, i) => {
                            const x = (i / Math.max(timeSeriesData.length - 1, 1)) * 400;
                            const maxCost = Math.max(...timeSeriesData.map(item => item.cost));
                            const y = 120 - (d.cost / maxCost) * 100;
                            return `${x},${y}`;
                          }).join(' ')}
                        />

                        {/* Area fill */}
                        <polygon
                          fill="url(#costGradient)"
                          points={`0,120 ${timeSeriesData.map((d, i) => {
                            const x = (i / Math.max(timeSeriesData.length - 1, 1)) * 400;
                            const maxCost = Math.max(...timeSeriesData.map(item => item.cost));
                            const y = 120 - (d.cost / maxCost) * 100;
                            return `${x},${y}`;
                          }).join(' ')} 400,120`}
                        />
                      </svg>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <ChartBarIcon className="h-8 w-8 mx-auto mb-2 text-gray-500" />
                        <p className="text-sm text-gray-500">No data available</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Latency Chart */}
              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h3 className="text-lg font-semibold text-white">Latency</h3>
                    <div className="flex items-center space-x-2 mt-2">
                      <span className="text-3xl font-bold text-white">
                        {Math.round(summary?.average_latency || 0)}ms
                      </span>
                      {latencyTrend && (
                        <span className={`text-sm px-2 py-1 rounded flex items-center space-x-1 ${
                          latencyTrend.isPositive ? 'text-red-400 bg-red-400/10' : 'text-green-400 bg-green-400/10'
                        }`}>
                          {latencyTrend.isPositive ? (
                            <ArrowTrendingUpIcon className="w-3 h-3" />
                          ) : (
                            <ArrowTrendingDownIcon className="w-3 h-3" />
                          )}
                          <span>{latencyTrend.percentage.toFixed(1)}%</span>
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="text-gray-500">
                    <ClockIcon className="w-6 h-6" />
                  </div>
                </div>

                {/* Latency Chart Visualization */}
                <div className="h-48 relative bg-gray-800/50 rounded">
                  {timeSeriesData.length > 0 ? (
                    <div className="absolute inset-4">
                      <svg className="w-full h-full" viewBox="0 0 400 120">
                        <defs>
                          <linearGradient id="latencyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" stopColor="#f59e0b" stopOpacity="0.3" />
                            <stop offset="100%" stopColor="#f59e0b" stopOpacity="0" />
                          </linearGradient>
                        </defs>

                        {/* Grid lines */}
                        {[...Array(5)].map((_, i) => (
                          <line
                            key={i}
                            x1="0"
                            y1={i * 24}
                            x2="400"
                            y2={i * 24}
                            stroke="#374151"
                            strokeWidth="0.5"
                            opacity="0.3"
                          />
                        ))}

                        {/* Latency line */}
                        <polyline
                          fill="none"
                          stroke="#f59e0b"
                          strokeWidth="2"
                          points={timeSeriesData.map((d, i) => {
                            const x = (i / Math.max(timeSeriesData.length - 1, 1)) * 400;
                            const maxLatency = Math.max(...timeSeriesData.map(item => item.latency || 0));
                            const y = 120 - ((d.latency || 0) / Math.max(maxLatency, 1)) * 100;
                            return `${x},${y}`;
                          }).join(' ')}
                        />

                        {/* Area fill */}
                        <polygon
                          fill="url(#latencyGradient)"
                          points={`0,120 ${timeSeriesData.map((d, i) => {
                            const x = (i / Math.max(timeSeriesData.length - 1, 1)) * 400;
                            const maxLatency = Math.max(...timeSeriesData.map(item => item.latency || 0));
                            const y = 120 - ((d.latency || 0) / Math.max(maxLatency, 1)) * 100;
                            return `${x},${y}`;
                          }).join(' ')} 400,120`}
                        />
                      </svg>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <ClockIcon className="h-8 w-8 mx-auto mb-2 text-gray-500" />
                        <p className="text-sm text-gray-500">No data available</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Bottom Section - Token Usage and Provider Breakdown */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Tokens Used */}
              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-white">Tokens Used</h3>
                    <p className="text-sm text-gray-400 mt-1">March 28</p>
                  </div>
                  <div className="text-gray-500">
                    <CpuChipIcon className="w-6 h-6" />
                  </div>
                </div>
                <div className="flex items-center space-x-2 mb-6">
                  <span className="text-3xl font-bold text-white">
                    {formatNumber(summary?.total_tokens || 0)}
                  </span>
                  <span className="text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1">
                    <ArrowTrendingUpIcon className="w-3 h-3" />
                    <span>8.39%</span>
                  </span>
                </div>

                {/* Token Scatter Plot */}
                <div className="h-32 relative bg-gray-800/50 rounded">
                  <div className="absolute inset-4">
                    {/* Legend */}
                    <div className="flex items-center space-x-4 mb-4 text-xs">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                        <span className="text-gray-400">Input Token</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        <span className="text-gray-400">Output Token</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                        <span className="text-gray-400">Total Token</span>
                      </div>
                    </div>

                    {/* Scatter dots */}
                    <div className="relative h-16">
                      {[...Array(20)].map((_, i) => (
                        <div
                          key={i}
                          className={`absolute w-1 h-1 rounded-full ${
                            i % 3 === 0 ? 'bg-yellow-500' :
                            i % 3 === 1 ? 'bg-green-500' : 'bg-blue-500'
                          }`}
                          style={{
                            left: `${Math.random() * 90}%`,
                            top: `${Math.random() * 80}%`,
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Requests */}
              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-white">Requests</h3>
                  </div>
                  <div className="text-gray-500">
                    <GlobeAltIcon className="w-6 h-6" />
                  </div>
                </div>
                <div className="flex items-center space-x-2 mb-6">
                  <span className="text-3xl font-bold text-white">
                    {formatNumber(summary?.total_requests || 0)}
                  </span>
                  <span className="text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1">
                    <ArrowTrendingUpIcon className="w-3 h-3" />
                    <span>3.39%</span>
                  </span>
                </div>

                {/* Horizontal Bar Chart */}
                <div className="space-y-3">
                  {analyticsData?.grouped_data.slice(0, 5).map((provider, index) => (
                    <div key={provider.name} className="flex items-center">
                      <div className="w-20 text-sm text-gray-400 truncate">
                        {provider.name}
                      </div>
                      <div className="flex-1 mx-3">
                        <div className="h-2 bg-gray-800 rounded-full overflow-hidden">
                          <div
                            className={`h-full rounded-full ${
                              index === 0 ? 'bg-pink-500' :
                              index === 1 ? 'bg-purple-500' :
                              index === 2 ? 'bg-cyan-500' :
                              index === 3 ? 'bg-green-500' : 'bg-yellow-500'
                            }`}
                            style={{
                              width: `${(provider.requests / (summary?.total_requests || 1)) * 100}%`
                            }}
                          />
                        </div>
                      </div>
                      <div className="text-sm text-gray-400 w-12 text-right">
                        {formatNumber(provider.requests)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Unique Users */}
              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-white">Unique Users</h3>
                  </div>
                  <div className="text-gray-500">
                    <UsersIcon className="w-6 h-6" />
                  </div>
                </div>
                <div className="flex items-center space-x-2 mb-6">
                  <span className="text-3xl font-bold text-white">
                    {summary?.successful_requests || 0}
                  </span>
                  <span className="text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1">
                    <ArrowTrendingUpIcon className="w-3 h-3" />
                    <span>3.39%</span>
                  </span>
                </div>

                {/* Wave Chart */}
                <div className="h-32 relative bg-gray-800/50 rounded">
                  <div className="absolute inset-4">
                    <svg className="w-full h-full" viewBox="0 0 200 80">
                      <defs>
                        <linearGradient id="waveGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                          <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.3" />
                          <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0" />
                        </linearGradient>
                      </defs>

                      {/* Wave path */}
                      <path
                        d="M0,40 Q50,20 100,40 T200,40"
                        fill="none"
                        stroke="#8b5cf6"
                        strokeWidth="2"
                      />

                      {/* Wave area */}
                      <path
                        d="M0,40 Q50,20 100,40 T200,40 L200,80 L0,80 Z"
                        fill="url(#waveGradient)"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Users Tab */}
        {activeTab === 'users' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Active Users</h3>
                  <UsersIcon className="w-6 h-6 text-gray-400" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">
                  {summary?.unique_users || 0}
                </div>
                <p className="text-sm text-gray-400">Users with API activity</p>
              </div>

              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">API Keys Generated</h3>
                  <KeyIcon className="w-6 h-6 text-gray-400" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">
                  {formatNumber(summary?.total_api_keys || 0)}
                </div>
                <p className="text-sm text-gray-400">User-generated API keys</p>
              </div>

              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Subscription Tiers</h3>
                  <CreditCardIcon className="w-6 h-6 text-gray-400" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Free</span>
                    <span className="text-white">-</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Paid</span>
                    <span className="text-white">-</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
              <h3 className="text-lg font-semibold text-white mb-4">User Activity Overview</h3>
              <p className="text-gray-400">Detailed user analytics and activity patterns will be displayed here based on your user_profiles, user_generated_api_keys, and request_logs data.</p>
            </div>
          </div>
        )}

        {/* Errors Tab */}
        {activeTab === 'errors' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Error Rate</h3>
                  <ExclamationTriangleIcon className="w-6 h-6 text-red-400" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">
                  {summary?.error_rate ? `${summary.error_rate.toFixed(2)}%` : '0%'}
                </div>
                <p className="text-sm text-gray-400">Failed requests percentage</p>
              </div>

              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Total Errors</h3>
                  <XCircleIcon className="w-6 h-6 text-red-400" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">
                  {formatNumber(summary?.failed_requests || 0)}
                </div>
                <p className="text-sm text-gray-400">Failed requests count</p>
              </div>

              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Error Sources</h3>
                  <BugAntIcon className="w-6 h-6 text-red-400" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">API Errors</span>
                    <span className="text-white">-</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Workflow Errors</span>
                    <span className="text-white">-</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
              <h3 className="text-lg font-semibold text-white mb-4">Error Analysis</h3>
              <p className="text-gray-400">Detailed error tracking and analysis will be displayed here based on your request_logs error_message and error_source data.</p>
            </div>
          </div>
        )}

        {/* Cache Tab */}
        {activeTab === 'cache' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Cache Hit Rate</h3>
                  <ServerIcon className="w-6 h-6 text-green-400" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">
                  {summary?.cache_hit_rate ? `${summary.cache_hit_rate.toFixed(1)}%` : '0%'}
                </div>
                <p className="text-sm text-gray-400">Semantic cache efficiency</p>
              </div>

              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Cache Entries</h3>
                  <CircleStackIcon className="w-6 h-6 text-blue-400" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">
                  {formatNumber(summary?.cache_entries || 0)}
                </div>
                <p className="text-sm text-gray-400">Stored cache responses</p>
              </div>

              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Cost Savings</h3>
                  <CurrencyDollarIcon className="w-6 h-6 text-green-400" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">
                  {formatCurrency(summary?.cache_savings || 0)}
                </div>
                <p className="text-sm text-gray-400">Saved through caching</p>
              </div>
            </div>

            <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
              <h3 className="text-lg font-semibold text-white mb-4">Cache Performance</h3>
              <p className="text-gray-400">Detailed cache analytics including semantic cache hits, response cache performance, and cost optimization metrics will be displayed here.</p>
            </div>
          </div>
        )}

        {/* Feedback Tab */}
        {activeTab === 'feedback' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Average Rating</h3>
                  <StarIcon className="w-6 h-6 text-yellow-400" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">
                  {summary?.avg_rating ? summary.avg_rating.toFixed(1) : '0.0'}
                </div>
                <p className="text-sm text-gray-400">User satisfaction score</p>
              </div>

              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Quality Score</h3>
                  <ChartBarIcon className="w-6 h-6 text-purple-400" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">
                  {summary?.avg_quality_score ? summary.avg_quality_score.toFixed(1) : '0.0'}
                </div>
                <p className="text-sm text-gray-400">Response quality metric</p>
              </div>

              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Feedback Count</h3>
                  <ChatBubbleLeftRightIcon className="w-6 h-6 text-cyan-400" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">
                  {formatNumber(summary?.feedback_count || 0)}
                </div>
                <p className="text-sm text-gray-400">Total feedback received</p>
              </div>
            </div>

            <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
              <h3 className="text-lg font-semibold text-white mb-4">Feedback Analysis</h3>
              <p className="text-gray-400">Detailed feedback analytics including user ratings, quality scores, behavioral signals, and A/B testing results from your routing_quality_metrics data.</p>
            </div>
          </div>
        )}

        {/* Metadata Tab */}
        {activeTab === 'metadata' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Routing Strategies</h3>
                  <DocumentTextIcon className="w-6 h-6 text-indigo-400" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Intelligent Role</span>
                    <span className="text-white">-</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Cost Optimized</span>
                    <span className="text-white">-</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Model Usage</h3>
                  <CpuChipIcon className="w-6 h-6 text-green-400" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">
                  {analyticsData?.grouped_data?.length || 0}
                </div>
                <p className="text-sm text-gray-400">Unique models used</p>
              </div>

              <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Configurations</h3>
                  <CogIcon className="w-6 h-6 text-orange-400" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">
                  {summary?.total_configs || 0}
                </div>
                <p className="text-sm text-gray-400">Active API configurations</p>
              </div>
            </div>

            <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50">
              <h3 className="text-lg font-semibold text-white mb-4">System Metadata</h3>
              <p className="text-gray-400">Comprehensive metadata analysis including request/response metadata, routing information, model parameters, and configuration details from your JSONB metadata fields.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default function AnalyticsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-[#040716] text-white">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="animate-pulse space-y-8">
            <div className="h-8 bg-gray-800 rounded w-1/3"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-gray-900 rounded-lg p-6 border border-gray-800">
                  <div className="h-4 bg-gray-800 rounded w-1/2 mb-4"></div>
                  <div className="h-8 bg-gray-800 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-800 rounded w-1/3"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    }>
      <AnalyticsPageContent />
    </Suspense>
  );
}
