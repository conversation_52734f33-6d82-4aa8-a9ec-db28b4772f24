"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ServerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BugAntIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BugAntIcon,ChartBarIcon,ChatBubbleLeftRightIcon,CircleStackIcon,ClockIcon,CogIcon,CpuChipIcon,CurrencyDollarIcon,DocumentTextIcon,ExclamationTriangleIcon,EyeIcon,GlobeAltIcon,KeyIcon,MagnifyingGlassIcon,ServerIcon,StarIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Utility functions\nconst formatNumber = (num)=>{\n    if (num >= 1000000) return \"\".concat((num / 1000000).toFixed(1), \"M\");\n    if (num >= 1000) return \"\".concat((num / 1000).toFixed(1), \"K\");\n    return num.toString();\n};\nconst formatCurrency = (amount)=>{\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD',\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 4\n    }).format(amount);\n};\nconst calculateTrend = (current, previous)=>{\n    if (previous === 0) return {\n        percentage: 0,\n        isPositive: current > 0\n    };\n    const percentage = (current - previous) / previous * 100;\n    return {\n        percentage: Math.abs(percentage),\n        isPositive: percentage >= 0\n    };\n};\n// MetricCard Component\nconst MetricCard = (param)=>{\n    let { title, value, trend, icon, subtitle } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-400 mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: typeof value === 'number' ? formatNumber(value) : value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 11\n                                }, undefined),\n                                trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm px-2 py-1 rounded-md flex items-center space-x-1 \".concat(trend.isPositive ? 'text-green-400 bg-green-400/10' : 'text-red-400 bg-red-400/10'),\n                                    children: [\n                                        trend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                trend.percentage.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 9\n                        }, undefined),\n                        subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: subtitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 22\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 102,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 101,\n        columnNumber: 3\n    }, undefined);\n};\n_c = MetricCard;\nfunction AnalyticsPageContent() {\n    var _subscription_user, _subscription_user1, _usersData_userProfile, _usersData_userProfile1, _usersData_successRate, _usersData_topModels, _analyticsData_grouped_data;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const subscription = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription)();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previousPeriodData, setPreviousPeriodData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeSeriesData, setTimeSeriesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Tab-specific data states\n    const [usersData, setUsersData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [errorsData, setErrorsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cacheData, setCacheData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [feedbackData, setFeedbackData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [metadataData, setMetadataData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tabLoading, setTabLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Filters\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('30');\n    const [selectedConfig, setSelectedConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            fetchCustomConfigs();\n        }\n    }[\"AnalyticsPageContent.useEffect\"], []);\n    // Fetch tab-specific data functions\n    const fetchUsersData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnalyticsPageContent.useCallback[fetchUsersData]\": async ()=>{\n            if (tabLoading.users) return;\n            setTabLoading({\n                \"AnalyticsPageContent.useCallback[fetchUsersData]\": (prev)=>({\n                        ...prev,\n                        users: true\n                    })\n            }[\"AnalyticsPageContent.useCallback[fetchUsersData]\"]);\n            try {\n                const response = await fetch(\"/api/analytics/users?days=\".concat(timeRange));\n                if (response.ok) {\n                    const result = await response.json();\n                    setUsersData(result.data);\n                }\n            } catch (error) {\n                console.error('Error fetching users data:', error);\n            } finally{\n                setTabLoading({\n                    \"AnalyticsPageContent.useCallback[fetchUsersData]\": (prev)=>({\n                            ...prev,\n                            users: false\n                        })\n                }[\"AnalyticsPageContent.useCallback[fetchUsersData]\"]);\n            }\n        }\n    }[\"AnalyticsPageContent.useCallback[fetchUsersData]\"], [\n        timeRange,\n        tabLoading.users\n    ]);\n    const fetchErrorsData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnalyticsPageContent.useCallback[fetchErrorsData]\": async ()=>{\n            if (tabLoading.errors) return;\n            setTabLoading({\n                \"AnalyticsPageContent.useCallback[fetchErrorsData]\": (prev)=>({\n                        ...prev,\n                        errors: true\n                    })\n            }[\"AnalyticsPageContent.useCallback[fetchErrorsData]\"]);\n            try {\n                const response = await fetch(\"/api/analytics/errors?days=\".concat(timeRange));\n                if (response.ok) {\n                    const result = await response.json();\n                    setErrorsData(result.data);\n                }\n            } catch (error) {\n                console.error('Error fetching errors data:', error);\n            } finally{\n                setTabLoading({\n                    \"AnalyticsPageContent.useCallback[fetchErrorsData]\": (prev)=>({\n                            ...prev,\n                            errors: false\n                        })\n                }[\"AnalyticsPageContent.useCallback[fetchErrorsData]\"]);\n            }\n        }\n    }[\"AnalyticsPageContent.useCallback[fetchErrorsData]\"], [\n        timeRange,\n        tabLoading.errors\n    ]);\n    const fetchCacheData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnalyticsPageContent.useCallback[fetchCacheData]\": async ()=>{\n            if (tabLoading.cache) return;\n            setTabLoading({\n                \"AnalyticsPageContent.useCallback[fetchCacheData]\": (prev)=>({\n                        ...prev,\n                        cache: true\n                    })\n            }[\"AnalyticsPageContent.useCallback[fetchCacheData]\"]);\n            try {\n                const response = await fetch(\"/api/analytics/cache?days=\".concat(timeRange));\n                if (response.ok) {\n                    const result = await response.json();\n                    setCacheData(result.data);\n                }\n            } catch (error) {\n                console.error('Error fetching cache data:', error);\n            } finally{\n                setTabLoading({\n                    \"AnalyticsPageContent.useCallback[fetchCacheData]\": (prev)=>({\n                            ...prev,\n                            cache: false\n                        })\n                }[\"AnalyticsPageContent.useCallback[fetchCacheData]\"]);\n            }\n        }\n    }[\"AnalyticsPageContent.useCallback[fetchCacheData]\"], [\n        timeRange,\n        tabLoading.cache\n    ]);\n    const fetchFeedbackData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnalyticsPageContent.useCallback[fetchFeedbackData]\": async ()=>{\n            if (tabLoading.feedback) return;\n            setTabLoading({\n                \"AnalyticsPageContent.useCallback[fetchFeedbackData]\": (prev)=>({\n                        ...prev,\n                        feedback: true\n                    })\n            }[\"AnalyticsPageContent.useCallback[fetchFeedbackData]\"]);\n            try {\n                const response = await fetch(\"/api/analytics/feedback?days=\".concat(timeRange));\n                if (response.ok) {\n                    const result = await response.json();\n                    setFeedbackData(result.data);\n                }\n            } catch (error) {\n                console.error('Error fetching feedback data:', error);\n            } finally{\n                setTabLoading({\n                    \"AnalyticsPageContent.useCallback[fetchFeedbackData]\": (prev)=>({\n                            ...prev,\n                            feedback: false\n                        })\n                }[\"AnalyticsPageContent.useCallback[fetchFeedbackData]\"]);\n            }\n        }\n    }[\"AnalyticsPageContent.useCallback[fetchFeedbackData]\"], [\n        timeRange,\n        tabLoading.feedback\n    ]);\n    const fetchMetadataData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnalyticsPageContent.useCallback[fetchMetadataData]\": async ()=>{\n            if (tabLoading.metadata) return;\n            setTabLoading({\n                \"AnalyticsPageContent.useCallback[fetchMetadataData]\": (prev)=>({\n                        ...prev,\n                        metadata: true\n                    })\n            }[\"AnalyticsPageContent.useCallback[fetchMetadataData]\"]);\n            try {\n                const response = await fetch(\"/api/analytics/metadata?days=\".concat(timeRange));\n                if (response.ok) {\n                    const result = await response.json();\n                    setMetadataData(result.data);\n                }\n            } catch (error) {\n                console.error('Error fetching metadata data:', error);\n            } finally{\n                setTabLoading({\n                    \"AnalyticsPageContent.useCallback[fetchMetadataData]\": (prev)=>({\n                            ...prev,\n                            metadata: false\n                        })\n                }[\"AnalyticsPageContent.useCallback[fetchMetadataData]\"]);\n            }\n        }\n    }[\"AnalyticsPageContent.useCallback[fetchMetadataData]\"], [\n        timeRange,\n        tabLoading.metadata\n    ]);\n    // Fetch data when tab changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            switch(activeTab){\n                case 'users':\n                    if (!usersData) fetchUsersData();\n                    break;\n                case 'errors':\n                    if (!errorsData) fetchErrorsData();\n                    break;\n                case 'cache':\n                    if (!cacheData) fetchCacheData();\n                    break;\n                case 'feedback':\n                    if (!feedbackData) fetchFeedbackData();\n                    break;\n                case 'metadata':\n                    if (!metadataData) fetchMetadataData();\n                    break;\n            }\n        }\n    }[\"AnalyticsPageContent.useEffect\"], [\n        activeTab,\n        usersData,\n        errorsData,\n        cacheData,\n        feedbackData,\n        metadataData,\n        fetchUsersData,\n        fetchErrorsData,\n        fetchCacheData,\n        fetchFeedbackData,\n        fetchMetadataData\n    ]);\n    // Refetch data when time range changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            if (activeTab !== 'overview') {\n                switch(activeTab){\n                    case 'users':\n                        setUsersData(null);\n                        fetchUsersData();\n                        break;\n                    case 'errors':\n                        setErrorsData(null);\n                        fetchErrorsData();\n                        break;\n                    case 'cache':\n                        setCacheData(null);\n                        fetchCacheData();\n                        break;\n                    case 'feedback':\n                        setFeedbackData(null);\n                        fetchFeedbackData();\n                        break;\n                    case 'metadata':\n                        setMetadataData(null);\n                        fetchMetadataData();\n                        break;\n                }\n            }\n        }\n    }[\"AnalyticsPageContent.useEffect\"], [\n        timeRange\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            var _subscription_user, _subscription_user1;\n            console.log('[Analytics] Subscription state:', {\n                subscription: !!subscription,\n                user: subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id,\n                loading: subscription === null || subscription === void 0 ? void 0 : subscription.loading,\n                error: subscription === null || subscription === void 0 ? void 0 : subscription.error,\n                isAuthenticated: subscription === null || subscription === void 0 ? void 0 : subscription.isAuthenticated\n            });\n            // If subscription hook is still loading, wait\n            if ((subscription === null || subscription === void 0 ? void 0 : subscription.loading) === true) {\n                console.log('[Analytics] Subscription still loading, waiting...');\n                return;\n            }\n            // If we have a user, fetch analytics data\n            if (subscription === null || subscription === void 0 ? void 0 : (_subscription_user1 = subscription.user) === null || _subscription_user1 === void 0 ? void 0 : _subscription_user1.id) {\n                console.log('[Analytics] User authenticated, fetching data...');\n                setError(null); // Clear any previous errors\n                fetchAnalyticsData();\n            } else if ((subscription === null || subscription === void 0 ? void 0 : subscription.loading) === false) {\n                // Only show error if subscription has finished loading and no user\n                console.log('[Analytics] No user found after loading completed');\n                setError('Authentication required. Please log in to view analytics.');\n                setLoading(false);\n            }\n        // If subscription is undefined or loading is undefined, keep waiting\n        }\n    }[\"AnalyticsPageContent.useEffect\"], [\n        subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id,\n        subscription === null || subscription === void 0 ? void 0 : subscription.loading,\n        timeRange,\n        selectedConfig\n    ]);\n    const fetchCustomConfigs = async ()=>{\n        try {\n            const response = await fetch('/api/custom-configs');\n            if (response.ok) {\n                const configs = await response.json();\n                setCustomConfigs(configs);\n            }\n        } catch (err) {\n            console.error('Error fetching configs:', err);\n        }\n    };\n    const fetchAnalyticsData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnalyticsPageContent.useCallback[fetchAnalyticsData]\": async ()=>{\n            try {\n                var _subscription_user, _data_grouped_data, _data_summary, _data_summary1;\n                setLoading(true);\n                setError(null);\n                console.log('[Analytics] Starting data fetch for user:', subscription === null || subscription === void 0 ? void 0 : (_subscription_user = subscription.user) === null || _subscription_user === void 0 ? void 0 : _subscription_user.id);\n                // First, try a simple test to see if the API is accessible\n                try {\n                    const testResponse = await fetch('/api/analytics/summary?groupBy=provider');\n                    console.log('[Analytics] Test API response status:', testResponse.status);\n                    if (testResponse.status === 401) {\n                        throw new Error('Authentication required. Please log in to view analytics.');\n                    }\n                } catch (testErr) {\n                    console.error('[Analytics] Test API failed:', testErr);\n                }\n                // Build query parameters\n                const params = new URLSearchParams();\n                const startDate = new Date();\n                startDate.setDate(startDate.getDate() - parseInt(timeRange));\n                params.append('startDate', startDate.toISOString());\n                if (selectedConfig) {\n                    params.append('customApiConfigId', selectedConfig);\n                }\n                console.log('[Analytics] Fetching with params:', params.toString());\n                // Fetch just the main analytics data first\n                const response = await fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=provider\"));\n                console.log('[Analytics] Response status:', response.status);\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    console.error('[Analytics] API Error:', errorText);\n                    if (response.status === 401) {\n                        throw new Error('Authentication required. Please log in to view analytics.');\n                    }\n                    throw new Error(\"Failed to fetch analytics data: \".concat(response.status, \" \").concat(errorText));\n                }\n                const data = await response.json();\n                console.log('[Analytics] Received data:', {\n                    summary: data.summary,\n                    groupedDataCount: ((_data_grouped_data = data.grouped_data) === null || _data_grouped_data === void 0 ? void 0 : _data_grouped_data.length) || 0,\n                    hasData: ((_data_summary = data.summary) === null || _data_summary === void 0 ? void 0 : _data_summary.total_requests) > 0\n                });\n                setAnalyticsData(data);\n                // Only fetch additional data if we have some basic data\n                if (((_data_summary1 = data.summary) === null || _data_summary1 === void 0 ? void 0 : _data_summary1.total_requests) > 0) {\n                    try {\n                        // Build previous period params for comparison\n                        const prevParams = new URLSearchParams();\n                        const prevStartDate = new Date();\n                        prevStartDate.setDate(prevStartDate.getDate() - parseInt(timeRange) * 2);\n                        const prevEndDate = new Date();\n                        prevEndDate.setDate(prevEndDate.getDate() - parseInt(timeRange));\n                        prevParams.append('startDate', prevStartDate.toISOString());\n                        prevParams.append('endDate', prevEndDate.toISOString());\n                        if (selectedConfig) {\n                            prevParams.append('customApiConfigId', selectedConfig);\n                        }\n                        // Fetch additional data\n                        const [prevResponse, timeSeriesResponse] = await Promise.all([\n                            fetch(\"/api/analytics/summary?\".concat(prevParams.toString(), \"&groupBy=provider\")),\n                            fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=day\"))\n                        ]);\n                        const prevData = prevResponse.ok ? await prevResponse.json() : null;\n                        const timeSeriesData = timeSeriesResponse.ok ? await timeSeriesResponse.json() : null;\n                        setPreviousPeriodData(prevData);\n                        if (timeSeriesData === null || timeSeriesData === void 0 ? void 0 : timeSeriesData.grouped_data) {\n                            const formattedTimeSeries = timeSeriesData.grouped_data.map({\n                                \"AnalyticsPageContent.useCallback[fetchAnalyticsData].formattedTimeSeries\": (item)=>({\n                                        date: item.period || item.name,\n                                        cost: item.cost || 0,\n                                        requests: item.requests || 0,\n                                        tokens: (item.input_tokens || 0) + (item.output_tokens || 0),\n                                        latency: item.avg_latency || 0\n                                    })\n                            }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData].formattedTimeSeries\"]);\n                            setTimeSeriesData(formattedTimeSeries);\n                            console.log('[Analytics] Formatted time series:', formattedTimeSeries.length, 'items');\n                        }\n                    } catch (additionalErr) {\n                        console.warn('[Analytics] Failed to fetch additional data:', additionalErr);\n                    // Don't fail the whole request if additional data fails\n                    }\n                }\n            } catch (err) {\n                console.error('[Analytics] Error fetching data:', err);\n                setError(err.message);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData]\"], [\n        timeRange,\n        selectedConfig\n    ]);\n    // Loading state - show loading if subscription is loading OR analytics data is loading\n    if ((subscription === null || subscription === void 0 ? void 0 : subscription.loading) !== false || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen w-full bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-8 bg-gray-800 rounded w-1/3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    ...Array(4)\n                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 rounded-lg p-6 border border-gray-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-800 rounded w-1/2 mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 bg-gray-800 rounded w-3/4 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-gray-800 rounded w-1/3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, i, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-4 right-4 bg-gray-800 rounded-lg p-3 flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-300\",\n                                children: (subscription === null || subscription === void 0 ? void 0 : subscription.loading) !== false ? 'Authenticating...' : 'Loading analytics...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 448,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 447,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen w-full bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Analytics\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900/20 border border-red-800 rounded-lg p-6 max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-400 mb-4\",\n                                    children: [\n                                        \"Error loading analytics: \",\n                                        error\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchAnalyticsData,\n                                    className: \"px-4 py-2 bg-cyan-500 text-white rounded hover:bg-cyan-600 transition-colors\",\n                                    children: \"Retry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 477,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 476,\n            columnNumber: 7\n        }, this);\n    }\n    const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n    const previousSummary = previousPeriodData === null || previousPeriodData === void 0 ? void 0 : previousPeriodData.summary;\n    // Calculate trends\n    const costTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0, previousSummary.total_cost) : undefined;\n    const requestTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0, previousSummary.total_requests) : undefined;\n    const latencyTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0, previousSummary.average_latency || 0) : undefined;\n    const successTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0, previousSummary.success_rate) : undefined;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-semibold text-white\",\n                                    children: \"Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search Filter\",\n                                                className: \"bg-transparent border-none outline-none text-gray-400 placeholder-gray-500 w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: timeRange,\n                                        onChange: (e)=>setTimeRange(e.target.value),\n                                        className: \"px-3 py-1 bg-gray-800 border border-gray-700 rounded text-sm text-white focus:outline-none focus:border-cyan-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"7\",\n                                                children: \"Last 7 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"30\",\n                                                children: \"Last 30 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"90\",\n                                                children: \"Last 90 days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 508,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 507,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('overview'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'overview' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('users'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'users' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Users\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 551,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('errors'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'errors' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Errors\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('cache'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'cache' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Cache\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('feedback'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'feedback' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Feedback\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('metadata'),\n                                className: \"flex items-center space-x-2 py-4 border-b-2 transition-colors \".concat(activeTab === 'metadata' ? 'border-cyan-500 text-cyan-500' : 'border-transparent text-gray-400 hover:text-white'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Metadata\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 538,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 537,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Total Request Made\",\n                                        value: (summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0,\n                                        trend: requestTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Average Latency\",\n                                        value: \"\".concat(Math.round((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0), \"ms\"),\n                                        trend: latencyTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"User Feedback\",\n                                        value: \"\".concat(((summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0).toFixed(1), \"%\"),\n                                        trend: successTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                                        title: \"Total Cost\",\n                                        value: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0),\n                                        trend: costTrend,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 13\n                            }, this),\n                            (!summary || summary.total_requests === 0) && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-16 w-16 mx-auto mb-4 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-2\",\n                                        children: \"No Analytics Data Yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"Start making API requests to see your analytics data here.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mb-6\",\n                                        children: \"Analytics will appear once you begin using your API configurations.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 17\n                                    }, this),\n                                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/50 rounded-lg p-4 text-left max-w-md mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-semibold text-white mb-2\",\n                                                children: \"Debug Info:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"User ID: \",\n                                                            (subscription === null || subscription === void 0 ? void 0 : (_subscription_user1 = subscription.user) === null || _subscription_user1 === void 0 ? void 0 : _subscription_user1.id) || 'Not logged in'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Time Range: \",\n                                                            timeRange,\n                                                            \" days\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Selected Config: \",\n                                                            selectedConfig || 'All configs'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Loading: \",\n                                                            loading ? 'Yes' : 'No'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Error: \",\n                                                            error || 'None'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    console.log('[Analytics] Manual refresh triggered');\n                                                    fetchAnalyticsData();\n                                                },\n                                                className: \"mt-3 px-3 py-1 bg-cyan-500 text-white text-xs rounded hover:bg-cyan-600 transition-colors\",\n                                                children: \"Refresh Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Cost\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-3xl font-bold text-white\",\n                                                                        children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 694,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    costTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm px-2 py-1 rounded flex items-center space-x-1 \".concat(costTrend.isPositive ? 'text-red-400 bg-red-400/10' : 'text-green-400 bg-green-400/10'),\n                                                                        children: [\n                                                                            costTrend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 702,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 704,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    costTrend.percentage.toFixed(1),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 706,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 698,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-48 relative bg-gray-800/50 rounded\",\n                                                children: timeSeriesData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 400 120\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"costGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#10b981\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 723,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#10b981\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 724,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 722,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 721,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            [\n                                                                ...Array(5)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                    x1: \"0\",\n                                                                    y1: i * 24,\n                                                                    x2: \"400\",\n                                                                    y2: i * 24,\n                                                                    stroke: \"#374151\",\n                                                                    strokeWidth: \"0.5\",\n                                                                    opacity: \"0.3\"\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 27\n                                                                }, this)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                fill: \"none\",\n                                                                stroke: \"#10b981\",\n                                                                strokeWidth: \"2\",\n                                                                points: timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxCost = Math.max(...timeSeriesData.map((item)=>item.cost));\n                                                                    const y = 120 - d.cost / maxCost * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' ')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                fill: \"url(#costGradient)\",\n                                                                points: \"0,120 \".concat(timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxCost = Math.max(...timeSeriesData.map((item)=>item.cost));\n                                                                    const y = 120 - d.cost / maxCost * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' '), \" 400,120\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 719,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-8 w-8 mx-auto mb-2 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 770,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"No data available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Latency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-3xl font-bold text-white\",\n                                                                        children: [\n                                                                            Math.round((summary === null || summary === void 0 ? void 0 : summary.average_latency) || 0),\n                                                                            \"ms\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 784,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    latencyTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm px-2 py-1 rounded flex items-center space-x-1 \".concat(latencyTrend.isPositive ? 'text-red-400 bg-red-400/10' : 'text-green-400 bg-green-400/10'),\n                                                                        children: [\n                                                                            latencyTrend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 792,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 794,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    latencyTrend.percentage.toFixed(1),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                lineNumber: 796,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 788,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 802,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-48 relative bg-gray-800/50 rounded\",\n                                                children: timeSeriesData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 400 120\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"latencyGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#f59e0b\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 813,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#f59e0b\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 814,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 811,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            [\n                                                                ...Array(5)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                    x1: \"0\",\n                                                                    y1: i * 24,\n                                                                    x2: \"400\",\n                                                                    y2: i * 24,\n                                                                    stroke: \"#374151\",\n                                                                    strokeWidth: \"0.5\",\n                                                                    opacity: \"0.3\"\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 820,\n                                                                    columnNumber: 27\n                                                                }, this)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                fill: \"none\",\n                                                                stroke: \"#f59e0b\",\n                                                                strokeWidth: \"2\",\n                                                                points: timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxLatency = Math.max(...timeSeriesData.map((item)=>item.latency || 0));\n                                                                    const y = 120 - (d.latency || 0) / Math.max(maxLatency, 1) * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' ')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 833,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                fill: \"url(#latencyGradient)\",\n                                                                points: \"0,120 \".concat(timeSeriesData.map((d, i)=>{\n                                                                    const x = i / Math.max(timeSeriesData.length - 1, 1) * 400;\n                                                                    const maxLatency = Math.max(...timeSeriesData.map((item)=>item.latency || 0));\n                                                                    const y = 120 - (d.latency || 0) / Math.max(maxLatency, 1) * 100;\n                                                                    return \"\".concat(x, \",\").concat(y);\n                                                                }).join(' '), \" 400,120\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 846,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 810,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-8 w-8 mx-auto mb-2 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 860,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"No data available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 861,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 859,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 858,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 687,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-white\",\n                                                                children: \"Tokens Used\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 875,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400 mt-1\",\n                                                                children: \"March 28\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 876,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 874,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 879,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 878,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 873,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.total_tokens) || 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 887,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"8.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 888,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 886,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 882,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-32 relative bg-gray-800/50 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 mb-4 text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-yellow-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 898,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Input Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 899,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 897,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-green-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 902,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Output Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 903,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 901,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 906,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Total Token\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 907,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-16\",\n                                                            children: [\n                                                                ...Array(20)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute w-1 h-1 rounded-full \".concat(i % 3 === 0 ? 'bg-yellow-500' : i % 3 === 1 ? 'bg-green-500' : 'bg-blue-500'),\n                                                                    style: {\n                                                                        left: \"\".concat(Math.random() * 90, \"%\"),\n                                                                        top: \"\".concat(Math.random() * 80, \"%\")\n                                                                    }\n                                                                }, i, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 914,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 912,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 893,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 872,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 935,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 938,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 937,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 933,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 942,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 946,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"3.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 947,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 945,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data.slice(0, 5).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 text-sm text-gray-400 truncate\",\n                                                                children: provider.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 mx-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-2 bg-gray-800 rounded-full overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-full rounded-full \".concat(index === 0 ? 'bg-pink-500' : index === 1 ? 'bg-purple-500' : index === 2 ? 'bg-cyan-500' : index === 3 ? 'bg-green-500' : 'bg-yellow-500'),\n                                                                        style: {\n                                                                            width: \"\".concat(provider.requests / ((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 1) * 100, \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 960,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 959,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400 w-12 text-right\",\n                                                                children: formatNumber(provider.requests)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 973,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, provider.name, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 954,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 952,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Unique Users\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 985,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 987,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 983,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: (summary === null || summary === void 0 ? void 0 : summary.successful_requests) || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 996,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"3.39%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 997,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 995,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 991,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-32 relative bg-gray-800/50 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-full h-full\",\n                                                        viewBox: \"0 0 200 80\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                    id: \"waveGradient\",\n                                                                    x1: \"0%\",\n                                                                    y1: \"0%\",\n                                                                    x2: \"0%\",\n                                                                    y2: \"100%\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"0%\",\n                                                                            stopColor: \"#8b5cf6\",\n                                                                            stopOpacity: \"0.3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 1007,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                            offset: \"100%\",\n                                                                            stopColor: \"#8b5cf6\",\n                                                                            stopOpacity: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 1008,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1006,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1005,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0,40 Q50,20 100,40 T200,40\",\n                                                                fill: \"none\",\n                                                                stroke: \"#8b5cf6\",\n                                                                strokeWidth: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1013,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0,40 Q50,20 100,40 T200,40 L200,80 L0,80 Z\",\n                                                                fill: \"url(#waveGradient)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1021,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1003,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1002,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 982,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 870,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 613,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'users' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: tabLoading.users ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1038,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-3 text-gray-400\",\n                                    children: \"Loading users analytics...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1039,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1037,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Active Users\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1046,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-6 h-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1047,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1045,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-white mb-2\",\n                                                    children: (usersData === null || usersData === void 0 ? void 0 : usersData.activeUsers) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1049,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"Users with API activity\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1052,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1044,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"API Keys Generated\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1057,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-6 h-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1058,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1056,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-white mb-2\",\n                                                    children: formatNumber((usersData === null || usersData === void 0 ? void 0 : usersData.totalApiKeys) || 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1060,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"User-generated API keys\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1063,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1055,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Total Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1068,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-6 h-6 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1069,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1067,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-white mb-2\",\n                                                    children: formatNumber((usersData === null || usersData === void 0 ? void 0 : usersData.totalRequests) || 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1071,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: \"API requests made\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1074,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1066,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1043,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"User Profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1080,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Subscription Tier\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1083,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white capitalize\",\n                                                                    children: (usersData === null || usersData === void 0 ? void 0 : (_usersData_userProfile = usersData.userProfile) === null || _usersData_userProfile === void 0 ? void 0 : _usersData_userProfile.tier) || 'Free'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1084,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1082,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Account Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1087,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400 capitalize\",\n                                                                    children: (usersData === null || usersData === void 0 ? void 0 : (_usersData_userProfile1 = usersData.userProfile) === null || _usersData_userProfile1 === void 0 ? void 0 : _usersData_userProfile1.status) || 'Active'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1088,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1086,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Success Rate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1091,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white\",\n                                                                    children: [\n                                                                        (usersData === null || usersData === void 0 ? void 0 : (_usersData_successRate = usersData.successRate) === null || _usersData_successRate === void 0 ? void 0 : _usersData_successRate.toFixed(1)) || 0,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1092,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1090,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Total Configs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1095,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white\",\n                                                                    children: (usersData === null || usersData === void 0 ? void 0 : usersData.totalConfigs) || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1096,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1094,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1081,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1079,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"Top Models Used\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1102,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: (usersData === null || usersData === void 0 ? void 0 : (_usersData_topModels = usersData.topModels) === null || _usersData_topModels === void 0 ? void 0 : _usersData_topModels.slice(0, 5).map((model, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400 truncate\",\n                                                                    children: model.model\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1106,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white\",\n                                                                    children: model.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 1107,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, model.model, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1105,\n                                                            columnNumber: 25\n                                                        }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 text-sm\",\n                                                        children: \"No model usage data available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1110,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1103,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1101,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1078,\n                                    columnNumber: 17\n                                }, this),\n                                (usersData === null || usersData === void 0 ? void 0 : usersData.timeSeriesData) && usersData.timeSeriesData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-4\",\n                                            children: \"Activity Timeline\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1118,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-64 flex items-end space-x-2\",\n                                            children: usersData.timeSeriesData.map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-cyan-500 rounded-t\",\n                                                            style: {\n                                                                height: \"\".concat(Math.max(day.requests / Math.max(...usersData.timeSeriesData.map((d)=>d.requests)) * 200, 2), \"px\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1122,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500 mt-2 transform -rotate-45\",\n                                                            children: new Date(day.date).toLocaleDateString('en-US', {\n                                                                month: 'short',\n                                                                day: 'numeric'\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1128,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, day.date, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1121,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1119,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1117,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 1035,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'errors' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Error Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1147,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-6 h-6 text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1148,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1146,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: (summary === null || summary === void 0 ? void 0 : summary.error_rate) ? \"\".concat(summary.error_rate.toFixed(2), \"%\") : '0%'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Failed requests percentage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1153,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Total Errors\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1158,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-6 h-6 text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1159,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.failed_requests) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1161,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Failed requests count\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Error Sources\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1169,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-6 h-6 text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1170,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"API Errors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1174,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1175,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1173,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Workflow Errors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1178,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1179,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1177,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1172,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 1144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white mb-4\",\n                                        children: \"Error Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Detailed error tracking and analysis will be displayed here based on your request_logs error_message and error_source data.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 1185,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 1143,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'cache' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Cache Hit Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1198,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1199,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: (summary === null || summary === void 0 ? void 0 : summary.cache_hit_rate) ? \"\".concat(summary.cache_hit_rate.toFixed(1), \"%\") : '0%'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Semantic cache efficiency\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1204,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1196,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Cache Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1209,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-6 h-6 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1210,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.cache_entries) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1212,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Stored cache responses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1215,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Cost Savings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1221,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.cache_savings) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Saved through caching\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1226,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1218,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 1195,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white mb-4\",\n                                        children: \"Cache Performance\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1231,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Detailed cache analytics including semantic cache hits, response cache performance, and cost optimization metrics will be displayed here.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1232,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 1230,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 1194,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'feedback' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Average Rating\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-6 h-6 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1244,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1242,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: (summary === null || summary === void 0 ? void 0 : summary.avg_rating) ? summary.avg_rating.toFixed(1) : '0.0'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1246,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"User satisfaction score\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1249,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Quality Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1254,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-6 h-6 text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1255,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: (summary === null || summary === void 0 ? void 0 : summary.avg_quality_score) ? summary.avg_quality_score.toFixed(1) : '0.0'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Response quality metric\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1260,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Feedback Count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1265,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-6 h-6 text-cyan-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1266,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1264,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: formatNumber((summary === null || summary === void 0 ? void 0 : summary.feedback_count) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Total feedback received\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1271,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1263,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 1240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white mb-4\",\n                                        children: \"Feedback Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Detailed feedback analytics including user ratings, quality scores, behavioral signals, and A/B testing results from your routing_quality_metrics data.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1277,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 1275,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 1239,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'metadata' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Routing Strategies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-6 h-6 text-indigo-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1289,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Intelligent Role\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1293,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1294,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1292,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Cost Optimized\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1297,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1298,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1296,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1291,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1286,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Model Usage\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1305,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1306,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: (analyticsData === null || analyticsData === void 0 ? void 0 : (_analyticsData_grouped_data = analyticsData.grouped_data) === null || _analyticsData_grouped_data === void 0 ? void 0 : _analyticsData_grouped_data.length) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Unique models used\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1311,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1303,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Configurations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1316,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BugAntIcon_ChartBarIcon_ChatBubbleLeftRightIcon_CircleStackIcon_ClockIcon_CogIcon_CpuChipIcon_CurrencyDollarIcon_DocumentTextIcon_ExclamationTriangleIcon_EyeIcon_GlobeAltIcon_KeyIcon_MagnifyingGlassIcon_ServerIcon_StarIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-6 h-6 text-orange-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1317,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1315,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: (summary === null || summary === void 0 ? void 0 : summary.total_configs) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1319,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Active API configurations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1322,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1314,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 1285,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white mb-4\",\n                                        children: \"System Metadata\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Comprehensive metadata analysis including request/response metadata, routing information, model parameters, and configuration details from your JSONB metadata fields.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 1328,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 1326,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 1284,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 611,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 505,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPageContent, \"DH2Udz6re/GfIBU2jwXJTKB9ZwE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription\n    ];\n});\n_c1 = AnalyticsPageContent;\nfunction AnalyticsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#040716] text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-800 rounded w-1/3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1343,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                ...Array(4)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900 rounded-lg p-6 border border-gray-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-800 rounded w-1/2 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1347,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-800 rounded w-3/4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1348,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-800 rounded w-1/3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1349,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1346,\n                                    columnNumber: 17\n                                }, void 0))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1344,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1342,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 1341,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 1340,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 1357,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 1339,\n        columnNumber: 5\n    }, this);\n}\n_c2 = AnalyticsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MetricCard\");\n$RefreshReg$(_c1, \"AnalyticsPageContent\");\n$RefreshReg$(_c2, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

});