/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analytics/users/route";
exports.ids = ["app/api/analytics/users/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fusers%2Froute&page=%2Fapi%2Fanalytics%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fusers%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fusers%2Froute&page=%2Fapi%2Fanalytics%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fusers%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_analytics_users_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analytics/users/route.ts */ \"(rsc)/./src/app/api/analytics/users/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analytics/users/route\",\n        pathname: \"/api/analytics/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/analytics/users/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\analytics\\\\users\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_analytics_users_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fusers%2Froute&page=%2Fapi%2Fanalytics%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fusers%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/analytics/users/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/analytics/users/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n        // Get authenticated user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const days = parseInt(searchParams.get('days') || '30');\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - days);\n        // Get user profile data\n        const { data: userProfile } = await supabase.from('user_profiles').select('subscription_tier, subscription_status, created_at').eq('id', user.id).single();\n        // Get total active users (users with API activity in the period)\n        const { data: activeUsersData } = await supabase.from('request_logs').select('user_id').eq('user_id', user.id).gte('request_timestamp', startDate.toISOString()).limit(1);\n        const activeUsers = activeUsersData && activeUsersData.length > 0 ? 1 : 0;\n        // Get user's API keys count\n        const { data: apiKeysData, count: apiKeysCount } = await supabase.from('user_generated_api_keys').select('id', {\n            count: 'exact'\n        }).eq('user_id', user.id);\n        // Get user's custom API configs count\n        const { data: configsData, count: configsCount } = await supabase.from('custom_api_configs').select('id', {\n            count: 'exact'\n        }).eq('user_id', user.id);\n        // Get user activity over time (requests per day)\n        const { data: activityData } = await supabase.from('request_logs').select('request_timestamp').eq('user_id', user.id).gte('request_timestamp', startDate.toISOString()).order('request_timestamp', {\n            ascending: true\n        });\n        // Process activity data into daily counts\n        const dailyActivity = {};\n        if (activityData) {\n            activityData.forEach((log)=>{\n                const date = new Date(log.request_timestamp).toISOString().split('T')[0];\n                dailyActivity[date] = (dailyActivity[date] || 0) + 1;\n            });\n        }\n        // Convert to time series format\n        const timeSeriesData = [];\n        for(let i = days - 1; i >= 0; i--){\n            const date = new Date();\n            date.setDate(date.getDate() - i);\n            const dateStr = date.toISOString().split('T')[0];\n            timeSeriesData.push({\n                date: dateStr,\n                requests: dailyActivity[dateStr] || 0\n            });\n        }\n        // Get user's total requests\n        const { data: totalRequestsData, count: totalRequests } = await supabase.from('request_logs').select('id', {\n            count: 'exact'\n        }).eq('user_id', user.id).gte('request_timestamp', startDate.toISOString());\n        // Get user's successful requests\n        const { data: successfulRequestsData, count: successfulRequests } = await supabase.from('request_logs').select('id', {\n            count: 'exact'\n        }).eq('user_id', user.id).gte('request_timestamp', startDate.toISOString()).gte('status_code', 200).lt('status_code', 300);\n        // Calculate success rate\n        const successRate = totalRequests > 0 ? successfulRequests / totalRequests * 100 : 0;\n        // Get subscription tier breakdown (for this user)\n        const subscriptionBreakdown = {\n            free: userProfile?.subscription_tier === 'free' ? 1 : 0,\n            starter: userProfile?.subscription_tier === 'starter' ? 1 : 0,\n            professional: userProfile?.subscription_tier === 'professional' ? 1 : 0,\n            enterprise: userProfile?.subscription_tier === 'enterprise' ? 1 : 0\n        };\n        // Get user's most used models\n        const { data: modelUsageData } = await supabase.from('request_logs').select('llm_model_name').eq('user_id', user.id).gte('request_timestamp', startDate.toISOString()).not('llm_model_name', 'is', null);\n        const modelUsage = {};\n        if (modelUsageData) {\n            modelUsageData.forEach((log)=>{\n                const model = log.llm_model_name;\n                modelUsage[model] = (modelUsage[model] || 0) + 1;\n            });\n        }\n        const topModels = Object.entries(modelUsage).sort(([, a], [, b])=>b - a).slice(0, 5).map(([model, count])=>({\n                model,\n                count\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                // Summary metrics\n                activeUsers,\n                totalApiKeys: apiKeysCount || 0,\n                totalConfigs: configsCount || 0,\n                totalRequests: totalRequests || 0,\n                successfulRequests: successfulRequests || 0,\n                successRate,\n                // User profile info\n                userProfile: {\n                    tier: userProfile?.subscription_tier || 'free',\n                    status: userProfile?.subscription_status || 'active',\n                    createdAt: userProfile?.created_at\n                },\n                // Subscription breakdown\n                subscriptionBreakdown,\n                // Activity data\n                timeSeriesData,\n                // Model usage\n                topModels,\n                // Period info\n                period: `${days} days`,\n                startDate: startDate.toISOString(),\n                endDate: new Date().toISOString()\n            }\n        });\n    } catch (error) {\n        console.error('Error in users analytics:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch users analytics',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analytics/users/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fusers%2Froute&page=%2Fapi%2Fanalytics%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fusers%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();