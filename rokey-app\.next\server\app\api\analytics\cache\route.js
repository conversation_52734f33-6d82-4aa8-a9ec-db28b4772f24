/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analytics/cache/route";
exports.ids = ["app/api/analytics/cache/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fcache%2Froute&page=%2Fapi%2Fanalytics%2Fcache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fcache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fcache%2Froute&page=%2Fapi%2Fanalytics%2Fcache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fcache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_analytics_cache_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analytics/cache/route.ts */ \"(rsc)/./src/app/api/analytics/cache/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analytics/cache/route\",\n        pathname: \"/api/analytics/cache\",\n        filename: \"route\",\n        bundlePath: \"app/api/analytics/cache/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\analytics\\\\cache\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_analytics_cache_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fcache%2Froute&page=%2Fapi%2Fanalytics%2Fcache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fcache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/analytics/cache/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/analytics/cache/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n        // Get authenticated user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const days = parseInt(searchParams.get('days') || '30');\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - days);\n        // Get semantic cache analytics data\n        const { data: cacheAnalyticsData } = await supabase.from('semantic_cache_analytics').select('*').eq('user_id', user.id).gte('date', startDate.toISOString().split('T')[0]).order('date', {\n            ascending: true\n        });\n        // Get total cache entries\n        const { data: cacheEntriesData, count: totalCacheEntries } = await supabase.from('semantic_cache').select('id', {\n            count: 'exact'\n        }).eq('user_id', user.id).gte('created_at', startDate.toISOString());\n        // Calculate cache statistics\n        let totalHits = 0;\n        let totalMisses = 0;\n        let totalCostSaved = 0;\n        let totalResponseTimeSaved = 0;\n        const dailyCacheData = {};\n        if (cacheAnalyticsData && cacheAnalyticsData.length > 0) {\n            cacheAnalyticsData.forEach((analytics)=>{\n                totalHits += analytics.cache_hits || 0;\n                totalMisses += analytics.cache_misses || 0;\n                totalCostSaved += parseFloat(analytics.cost_saved || '0');\n                totalResponseTimeSaved += analytics.response_time_saved_ms || 0;\n                dailyCacheData[analytics.date] = {\n                    hits: analytics.cache_hits || 0,\n                    misses: analytics.cache_misses || 0,\n                    costSaved: parseFloat(analytics.cost_saved || '0'),\n                    responseTimeSaved: analytics.response_time_saved_ms || 0\n                };\n            });\n        }\n        // Calculate cache hit rate\n        const totalRequests = totalHits + totalMisses;\n        const cacheHitRate = totalRequests > 0 ? totalHits / totalRequests * 100 : 0;\n        // Get cache entries by tier\n        const { data: cacheByTierData } = await supabase.from('semantic_cache').select('cache_tier').eq('user_id', user.id).gte('created_at', startDate.toISOString());\n        const cacheByTier = {};\n        if (cacheByTierData) {\n            cacheByTierData.forEach((entry)=>{\n                const tier = entry.cache_tier || 'unknown';\n                cacheByTier[tier] = (cacheByTier[tier] || 0) + 1;\n            });\n        }\n        // Get cache entries by provider\n        const { data: cacheByProviderData } = await supabase.from('semantic_cache').select('provider_used').eq('user_id', user.id).gte('created_at', startDate.toISOString());\n        const cacheByProvider = {};\n        if (cacheByProviderData) {\n            cacheByProviderData.forEach((entry)=>{\n                const provider = entry.provider_used || 'unknown';\n                cacheByProvider[provider] = (cacheByProvider[provider] || 0) + 1;\n            });\n        }\n        // Convert to time series format for charts\n        const cacheTimeSeriesData = [];\n        for(let i = days - 1; i >= 0; i--){\n            const date = new Date();\n            date.setDate(date.getDate() - i);\n            const dateStr = date.toISOString().split('T')[0];\n            const dayData = dailyCacheData[dateStr] || {\n                hits: 0,\n                misses: 0,\n                costSaved: 0,\n                responseTimeSaved: 0\n            };\n            cacheTimeSeriesData.push({\n                date: dateStr,\n                hits: dayData.hits,\n                misses: dayData.misses,\n                hitRate: dayData.hits + dayData.misses > 0 ? dayData.hits / (dayData.hits + dayData.misses) * 100 : 0,\n                costSaved: dayData.costSaved,\n                responseTimeSaved: dayData.responseTimeSaved\n            });\n        }\n        // Get top cached models\n        const { data: cachedModelsData } = await supabase.from('semantic_cache').select('model_used').eq('user_id', user.id).gte('created_at', startDate.toISOString());\n        const modelCacheUsage = {};\n        if (cachedModelsData) {\n            cachedModelsData.forEach((entry)=>{\n                const model = entry.model_used || 'unknown';\n                modelCacheUsage[model] = (modelCacheUsage[model] || 0) + 1;\n            });\n        }\n        const topCachedModels = Object.entries(modelCacheUsage).sort(([, a], [, b])=>b - a).slice(0, 10).map(([model, count])=>({\n                model,\n                count\n            }));\n        // Calculate cache efficiency metrics\n        const averageResponseTimeSaved = totalHits > 0 ? totalResponseTimeSaved / totalHits : 0;\n        const averageCostSavedPerHit = totalHits > 0 ? totalCostSaved / totalHits : 0;\n        // Get cache storage usage\n        const { data: cacheStorageData } = await supabase.from('semantic_cache').select('prompt_text, response_data').eq('user_id', user.id).gte('created_at', startDate.toISOString());\n        let totalStorageBytes = 0;\n        if (cacheStorageData) {\n            cacheStorageData.forEach((entry)=>{\n                const promptSize = new Blob([\n                    entry.prompt_text || ''\n                ]).size;\n                const responseSize = new Blob([\n                    JSON.stringify(entry.response_data || {})\n                ]).size;\n                totalStorageBytes += promptSize + responseSize;\n            });\n        }\n        // Calculate cache trend (compare with previous period)\n        const previousStartDate = new Date(startDate);\n        previousStartDate.setDate(previousStartDate.getDate() - days);\n        const { data: previousCacheData } = await supabase.from('semantic_cache_analytics').select('cache_hits, cache_misses').eq('user_id', user.id).gte('date', previousStartDate.toISOString().split('T')[0]).lt('date', startDate.toISOString().split('T')[0]);\n        let previousHits = 0;\n        let previousMisses = 0;\n        if (previousCacheData) {\n            previousCacheData.forEach((analytics)=>{\n                previousHits += analytics.cache_hits || 0;\n                previousMisses += analytics.cache_misses || 0;\n            });\n        }\n        const previousTotalRequests = previousHits + previousMisses;\n        const previousHitRate = previousTotalRequests > 0 ? previousHits / previousTotalRequests * 100 : 0;\n        const hitRateTrend = cacheHitRate - previousHitRate;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                // Summary metrics\n                totalCacheEntries: totalCacheEntries || 0,\n                totalHits,\n                totalMisses,\n                cacheHitRate,\n                hitRateTrend,\n                totalCostSaved,\n                totalResponseTimeSaved,\n                averageResponseTimeSaved,\n                averageCostSavedPerHit,\n                totalStorageBytes,\n                // Breakdowns\n                cacheByTier: Object.entries(cacheByTier).map(([tier, count])=>({\n                        tier,\n                        count\n                    })),\n                cacheByProvider: Object.entries(cacheByProvider).map(([provider, count])=>({\n                        provider,\n                        count\n                    })),\n                topCachedModels,\n                // Time series data\n                cacheTimeSeriesData,\n                // Period info\n                period: `${days} days`,\n                startDate: startDate.toISOString(),\n                endDate: new Date().toISOString()\n            }\n        });\n    } catch (error) {\n        console.error('Error in cache analytics:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch cache analytics',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hbmFseXRpY3MvY2FjaGUvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXdEO0FBQ3NCO0FBRXZFLGVBQWVFLElBQUlDLE9BQW9CO0lBQzVDLElBQUk7UUFDRixNQUFNQyxXQUFXSCwyRkFBcUNBLENBQUNFO1FBRXZELHlCQUF5QjtRQUN6QixNQUFNLEVBQUVFLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQUVDLE9BQU9DLFNBQVMsRUFBRSxHQUFHLE1BQU1KLFNBQVNLLElBQUksQ0FBQ0MsT0FBTztRQUN4RSxJQUFJRixhQUFhLENBQUNGLE1BQU07WUFDdEIsT0FBT04scURBQVlBLENBQUNXLElBQUksQ0FBQztnQkFBRUosT0FBTztZQUFlLEdBQUc7Z0JBQUVLLFFBQVE7WUFBSTtRQUNwRTtRQUVBLE1BQU0sRUFBRUMsWUFBWSxFQUFFLEdBQUcsSUFBSUMsSUFBSVgsUUFBUVksR0FBRztRQUM1QyxNQUFNQyxPQUFPQyxTQUFTSixhQUFhSyxHQUFHLENBQUMsV0FBVztRQUNsRCxNQUFNQyxZQUFZLElBQUlDO1FBQ3RCRCxVQUFVRSxPQUFPLENBQUNGLFVBQVVHLE9BQU8sS0FBS047UUFFeEMsb0NBQW9DO1FBQ3BDLE1BQU0sRUFBRVgsTUFBTWtCLGtCQUFrQixFQUFFLEdBQUcsTUFBTW5CLFNBQ3hDb0IsSUFBSSxDQUFDLDRCQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLFdBQVdwQixLQUFLcUIsRUFBRSxFQUNyQkMsR0FBRyxDQUFDLFFBQVFULFVBQVVVLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQ2pEQyxLQUFLLENBQUMsUUFBUTtZQUFFQyxXQUFXO1FBQUs7UUFFbkMsMEJBQTBCO1FBQzFCLE1BQU0sRUFBRTNCLE1BQU00QixnQkFBZ0IsRUFBRUMsT0FBT0MsaUJBQWlCLEVBQUUsR0FBRyxNQUFNL0IsU0FDaEVvQixJQUFJLENBQUMsa0JBQ0xDLE1BQU0sQ0FBQyxNQUFNO1lBQUVTLE9BQU87UUFBUSxHQUM5QlIsRUFBRSxDQUFDLFdBQVdwQixLQUFLcUIsRUFBRSxFQUNyQkMsR0FBRyxDQUFDLGNBQWNULFVBQVVVLFdBQVc7UUFFMUMsNkJBQTZCO1FBQzdCLElBQUlPLFlBQVk7UUFDaEIsSUFBSUMsY0FBYztRQUNsQixJQUFJQyxpQkFBaUI7UUFDckIsSUFBSUMseUJBQXlCO1FBQzdCLE1BQU1DLGlCQUFpQixDQUFDO1FBRXhCLElBQUlqQixzQkFBc0JBLG1CQUFtQmtCLE1BQU0sR0FBRyxHQUFHO1lBQ3ZEbEIsbUJBQW1CbUIsT0FBTyxDQUFDQyxDQUFBQTtnQkFDekJQLGFBQWFPLFVBQVVDLFVBQVUsSUFBSTtnQkFDckNQLGVBQWVNLFVBQVVFLFlBQVksSUFBSTtnQkFDekNQLGtCQUFrQlEsV0FBV0gsVUFBVUksVUFBVSxJQUFJO2dCQUNyRFIsMEJBQTBCSSxVQUFVSyxzQkFBc0IsSUFBSTtnQkFFOURSLGNBQWMsQ0FBQ0csVUFBVU0sSUFBSSxDQUFDLEdBQUc7b0JBQy9CQyxNQUFNUCxVQUFVQyxVQUFVLElBQUk7b0JBQzlCTyxRQUFRUixVQUFVRSxZQUFZLElBQUk7b0JBQ2xDTyxXQUFXTixXQUFXSCxVQUFVSSxVQUFVLElBQUk7b0JBQzlDTSxtQkFBbUJWLFVBQVVLLHNCQUFzQixJQUFJO2dCQUN6RDtZQUNGO1FBQ0Y7UUFFQSwyQkFBMkI7UUFDM0IsTUFBTU0sZ0JBQWdCbEIsWUFBWUM7UUFDbEMsTUFBTWtCLGVBQWVELGdCQUFnQixJQUFJLFlBQWFBLGdCQUFpQixNQUFNO1FBRTdFLDRCQUE0QjtRQUM1QixNQUFNLEVBQUVqRCxNQUFNbUQsZUFBZSxFQUFFLEdBQUcsTUFBTXBELFNBQ3JDb0IsSUFBSSxDQUFDLGtCQUNMQyxNQUFNLENBQUMsY0FDUEMsRUFBRSxDQUFDLFdBQVdwQixLQUFLcUIsRUFBRSxFQUNyQkMsR0FBRyxDQUFDLGNBQWNULFVBQVVVLFdBQVc7UUFFMUMsTUFBTTRCLGNBQWMsQ0FBQztRQUNyQixJQUFJRCxpQkFBaUI7WUFDbkJBLGdCQUFnQmQsT0FBTyxDQUFDZ0IsQ0FBQUE7Z0JBQ3RCLE1BQU1DLE9BQU9ELE1BQU1FLFVBQVUsSUFBSTtnQkFDakNILFdBQVcsQ0FBQ0UsS0FBSyxHQUFHLENBQUNGLFdBQVcsQ0FBQ0UsS0FBSyxJQUFJLEtBQUs7WUFDakQ7UUFDRjtRQUVBLGdDQUFnQztRQUNoQyxNQUFNLEVBQUV0RCxNQUFNd0QsbUJBQW1CLEVBQUUsR0FBRyxNQUFNekQsU0FDekNvQixJQUFJLENBQUMsa0JBQ0xDLE1BQU0sQ0FBQyxpQkFDUEMsRUFBRSxDQUFDLFdBQVdwQixLQUFLcUIsRUFBRSxFQUNyQkMsR0FBRyxDQUFDLGNBQWNULFVBQVVVLFdBQVc7UUFFMUMsTUFBTWlDLGtCQUFrQixDQUFDO1FBQ3pCLElBQUlELHFCQUFxQjtZQUN2QkEsb0JBQW9CbkIsT0FBTyxDQUFDZ0IsQ0FBQUE7Z0JBQzFCLE1BQU1LLFdBQVdMLE1BQU1NLGFBQWEsSUFBSTtnQkFDeENGLGVBQWUsQ0FBQ0MsU0FBUyxHQUFHLENBQUNELGVBQWUsQ0FBQ0MsU0FBUyxJQUFJLEtBQUs7WUFDakU7UUFDRjtRQUVBLDJDQUEyQztRQUMzQyxNQUFNRSxzQkFBc0IsRUFBRTtRQUM5QixJQUFLLElBQUlDLElBQUlsRCxPQUFPLEdBQUdrRCxLQUFLLEdBQUdBLElBQUs7WUFDbEMsTUFBTWpCLE9BQU8sSUFBSTdCO1lBQ2pCNkIsS0FBSzVCLE9BQU8sQ0FBQzRCLEtBQUszQixPQUFPLEtBQUs0QztZQUM5QixNQUFNQyxVQUFVbEIsS0FBS3BCLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1lBQ2hELE1BQU1zQyxVQUFVNUIsY0FBYyxDQUFDMkIsUUFBUSxJQUFJO2dCQUFFakIsTUFBTTtnQkFBR0MsUUFBUTtnQkFBR0MsV0FBVztnQkFBR0MsbUJBQW1CO1lBQUU7WUFFcEdZLG9CQUFvQkksSUFBSSxDQUFDO2dCQUN2QnBCLE1BQU1rQjtnQkFDTmpCLE1BQU1rQixRQUFRbEIsSUFBSTtnQkFDbEJDLFFBQVFpQixRQUFRakIsTUFBTTtnQkFDdEJtQixTQUFTLFFBQVNwQixJQUFJLEdBQUdrQixRQUFRakIsTUFBTSxHQUFJLElBQUksUUFBU0QsSUFBSSxHQUFJa0IsQ0FBQUEsUUFBUWxCLElBQUksR0FBR2tCLFFBQVFqQixNQUFNLElBQUssTUFBTTtnQkFDeEdDLFdBQVdnQixRQUFRaEIsU0FBUztnQkFDNUJDLG1CQUFtQmUsUUFBUWYsaUJBQWlCO1lBQzlDO1FBQ0Y7UUFFQSx3QkFBd0I7UUFDeEIsTUFBTSxFQUFFaEQsTUFBTWtFLGdCQUFnQixFQUFFLEdBQUcsTUFBTW5FLFNBQ3RDb0IsSUFBSSxDQUFDLGtCQUNMQyxNQUFNLENBQUMsY0FDUEMsRUFBRSxDQUFDLFdBQVdwQixLQUFLcUIsRUFBRSxFQUNyQkMsR0FBRyxDQUFDLGNBQWNULFVBQVVVLFdBQVc7UUFFMUMsTUFBTTJDLGtCQUFrQixDQUFDO1FBQ3pCLElBQUlELGtCQUFrQjtZQUNwQkEsaUJBQWlCN0IsT0FBTyxDQUFDZ0IsQ0FBQUE7Z0JBQ3ZCLE1BQU1lLFFBQVFmLE1BQU1nQixVQUFVLElBQUk7Z0JBQ2xDRixlQUFlLENBQUNDLE1BQU0sR0FBRyxDQUFDRCxlQUFlLENBQUNDLE1BQU0sSUFBSSxLQUFLO1lBQzNEO1FBQ0Y7UUFFQSxNQUFNRSxrQkFBa0JDLE9BQU9DLE9BQU8sQ0FBQ0wsaUJBQ3BDTSxJQUFJLENBQUMsQ0FBQyxHQUFFQyxFQUFFLEVBQUUsR0FBRUMsRUFBRSxHQUFLQSxJQUFJRCxHQUN6QkUsS0FBSyxDQUFDLEdBQUcsSUFDVEMsR0FBRyxDQUFDLENBQUMsQ0FBQ1QsT0FBT3ZDLE1BQU0sR0FBTTtnQkFBRXVDO2dCQUFPdkM7WUFBTTtRQUUzQyxxQ0FBcUM7UUFDckMsTUFBTWlELDJCQUEyQi9DLFlBQVksSUFBSUcseUJBQXlCSCxZQUFZO1FBQ3RGLE1BQU1nRCx5QkFBeUJoRCxZQUFZLElBQUlFLGlCQUFpQkYsWUFBWTtRQUU1RSwwQkFBMEI7UUFDMUIsTUFBTSxFQUFFL0IsTUFBTWdGLGdCQUFnQixFQUFFLEdBQUcsTUFBTWpGLFNBQ3RDb0IsSUFBSSxDQUFDLGtCQUNMQyxNQUFNLENBQUMsOEJBQ1BDLEVBQUUsQ0FBQyxXQUFXcEIsS0FBS3FCLEVBQUUsRUFDckJDLEdBQUcsQ0FBQyxjQUFjVCxVQUFVVSxXQUFXO1FBRTFDLElBQUl5RCxvQkFBb0I7UUFDeEIsSUFBSUQsa0JBQWtCO1lBQ3BCQSxpQkFBaUIzQyxPQUFPLENBQUNnQixDQUFBQTtnQkFDdkIsTUFBTTZCLGFBQWEsSUFBSUMsS0FBSztvQkFBQzlCLE1BQU0rQixXQUFXLElBQUk7aUJBQUcsRUFBRUMsSUFBSTtnQkFDM0QsTUFBTUMsZUFBZSxJQUFJSCxLQUFLO29CQUFDSSxLQUFLQyxTQUFTLENBQUNuQyxNQUFNb0MsYUFBYSxJQUFJLENBQUM7aUJBQUcsRUFBRUosSUFBSTtnQkFDL0VKLHFCQUFxQkMsYUFBYUk7WUFDcEM7UUFDRjtRQUVBLHVEQUF1RDtRQUN2RCxNQUFNSSxvQkFBb0IsSUFBSTNFLEtBQUtEO1FBQ25DNEUsa0JBQWtCMUUsT0FBTyxDQUFDMEUsa0JBQWtCekUsT0FBTyxLQUFLTjtRQUV4RCxNQUFNLEVBQUVYLE1BQU0yRixpQkFBaUIsRUFBRSxHQUFHLE1BQU01RixTQUN2Q29CLElBQUksQ0FBQyw0QkFDTEMsTUFBTSxDQUFDLDRCQUNQQyxFQUFFLENBQUMsV0FBV3BCLEtBQUtxQixFQUFFLEVBQ3JCQyxHQUFHLENBQUMsUUFBUW1FLGtCQUFrQmxFLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQ3pEbUUsRUFBRSxDQUFDLFFBQVE5RSxVQUFVVSxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtRQUVuRCxJQUFJb0UsZUFBZTtRQUNuQixJQUFJQyxpQkFBaUI7UUFDckIsSUFBSUgsbUJBQW1CO1lBQ3JCQSxrQkFBa0J0RCxPQUFPLENBQUNDLENBQUFBO2dCQUN4QnVELGdCQUFnQnZELFVBQVVDLFVBQVUsSUFBSTtnQkFDeEN1RCxrQkFBa0J4RCxVQUFVRSxZQUFZLElBQUk7WUFDOUM7UUFDRjtRQUVBLE1BQU11RCx3QkFBd0JGLGVBQWVDO1FBQzdDLE1BQU1FLGtCQUFrQkQsd0JBQXdCLElBQUksZUFBZ0JBLHdCQUF5QixNQUFNO1FBQ25HLE1BQU1FLGVBQWUvQyxlQUFlOEM7UUFFcEMsT0FBT3JHLHFEQUFZQSxDQUFDVyxJQUFJLENBQUM7WUFDdkI0RixTQUFTO1lBQ1RsRyxNQUFNO2dCQUNKLGtCQUFrQjtnQkFDbEI4QixtQkFBbUJBLHFCQUFxQjtnQkFDeENDO2dCQUNBQztnQkFDQWtCO2dCQUNBK0M7Z0JBQ0FoRTtnQkFDQUM7Z0JBQ0E0QztnQkFDQUM7Z0JBQ0FFO2dCQUVBLGFBQWE7Z0JBQ2I3QixhQUFhbUIsT0FBT0MsT0FBTyxDQUFDcEIsYUFBYXlCLEdBQUcsQ0FBQyxDQUFDLENBQUN2QixNQUFNekIsTUFBTSxHQUFNO3dCQUFFeUI7d0JBQU16QjtvQkFBTTtnQkFDL0U0QixpQkFBaUJjLE9BQU9DLE9BQU8sQ0FBQ2YsaUJBQWlCb0IsR0FBRyxDQUFDLENBQUMsQ0FBQ25CLFVBQVU3QixNQUFNLEdBQU07d0JBQUU2Qjt3QkFBVTdCO29CQUFNO2dCQUMvRnlDO2dCQUVBLG1CQUFtQjtnQkFDbkJWO2dCQUVBLGNBQWM7Z0JBQ2R1QyxRQUFRLEdBQUd4RixLQUFLLEtBQUssQ0FBQztnQkFDdEJHLFdBQVdBLFVBQVVVLFdBQVc7Z0JBQ2hDNEUsU0FBUyxJQUFJckYsT0FBT1MsV0FBVztZQUNqQztRQUNGO0lBRUYsRUFBRSxPQUFPdEIsT0FBTztRQUNkbUcsUUFBUW5HLEtBQUssQ0FBQyw2QkFBNkJBO1FBQzNDLE9BQU9QLHFEQUFZQSxDQUFDVyxJQUFJLENBQUM7WUFDdkJKLE9BQU87WUFDUG9HLFNBQVNwRyxpQkFBaUJxRyxRQUFRckcsTUFBTXNHLE9BQU8sR0FBRztRQUNwRCxHQUFHO1lBQUVqRyxRQUFRO1FBQUk7SUFDbkI7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGFwcFxcYXBpXFxhbmFseXRpY3NcXGNhY2hlXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgY3JlYXRlU3VwYWJhc2VTZXJ2ZXJDbGllbnRGcm9tUmVxdWVzdCB9IGZyb20gJ0AvbGliL3N1cGFiYXNlL3NlcnZlcic7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZVN1cGFiYXNlU2VydmVyQ2xpZW50RnJvbVJlcXVlc3QocmVxdWVzdCk7XG4gICAgXG4gICAgLy8gR2V0IGF1dGhlbnRpY2F0ZWQgdXNlclxuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0sIGVycm9yOiBhdXRoRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuICAgIGlmIChhdXRoRXJyb3IgfHwgIXVzZXIpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnVW5hdXRob3JpemVkJyB9LCB7IHN0YXR1czogNDAxIH0pO1xuICAgIH1cblxuICAgIGNvbnN0IHsgc2VhcmNoUGFyYW1zIH0gPSBuZXcgVVJMKHJlcXVlc3QudXJsKTtcbiAgICBjb25zdCBkYXlzID0gcGFyc2VJbnQoc2VhcmNoUGFyYW1zLmdldCgnZGF5cycpIHx8ICczMCcpO1xuICAgIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKCk7XG4gICAgc3RhcnREYXRlLnNldERhdGUoc3RhcnREYXRlLmdldERhdGUoKSAtIGRheXMpO1xuXG4gICAgLy8gR2V0IHNlbWFudGljIGNhY2hlIGFuYWx5dGljcyBkYXRhXG4gICAgY29uc3QgeyBkYXRhOiBjYWNoZUFuYWx5dGljc0RhdGEgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnc2VtYW50aWNfY2FjaGVfYW5hbHl0aWNzJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5ndGUoJ2RhdGUnLCBzdGFydERhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdKVxuICAgICAgLm9yZGVyKCdkYXRlJywgeyBhc2NlbmRpbmc6IHRydWUgfSk7XG5cbiAgICAvLyBHZXQgdG90YWwgY2FjaGUgZW50cmllc1xuICAgIGNvbnN0IHsgZGF0YTogY2FjaGVFbnRyaWVzRGF0YSwgY291bnQ6IHRvdGFsQ2FjaGVFbnRyaWVzIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3NlbWFudGljX2NhY2hlJylcbiAgICAgIC5zZWxlY3QoJ2lkJywgeyBjb3VudDogJ2V4YWN0JyB9KVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5ndGUoJ2NyZWF0ZWRfYXQnLCBzdGFydERhdGUudG9JU09TdHJpbmcoKSk7XG5cbiAgICAvLyBDYWxjdWxhdGUgY2FjaGUgc3RhdGlzdGljc1xuICAgIGxldCB0b3RhbEhpdHMgPSAwO1xuICAgIGxldCB0b3RhbE1pc3NlcyA9IDA7XG4gICAgbGV0IHRvdGFsQ29zdFNhdmVkID0gMDtcbiAgICBsZXQgdG90YWxSZXNwb25zZVRpbWVTYXZlZCA9IDA7XG4gICAgY29uc3QgZGFpbHlDYWNoZURhdGEgPSB7fTtcblxuICAgIGlmIChjYWNoZUFuYWx5dGljc0RhdGEgJiYgY2FjaGVBbmFseXRpY3NEYXRhLmxlbmd0aCA+IDApIHtcbiAgICAgIGNhY2hlQW5hbHl0aWNzRGF0YS5mb3JFYWNoKGFuYWx5dGljcyA9PiB7XG4gICAgICAgIHRvdGFsSGl0cyArPSBhbmFseXRpY3MuY2FjaGVfaGl0cyB8fCAwO1xuICAgICAgICB0b3RhbE1pc3NlcyArPSBhbmFseXRpY3MuY2FjaGVfbWlzc2VzIHx8IDA7XG4gICAgICAgIHRvdGFsQ29zdFNhdmVkICs9IHBhcnNlRmxvYXQoYW5hbHl0aWNzLmNvc3Rfc2F2ZWQgfHwgJzAnKTtcbiAgICAgICAgdG90YWxSZXNwb25zZVRpbWVTYXZlZCArPSBhbmFseXRpY3MucmVzcG9uc2VfdGltZV9zYXZlZF9tcyB8fCAwO1xuICAgICAgICBcbiAgICAgICAgZGFpbHlDYWNoZURhdGFbYW5hbHl0aWNzLmRhdGVdID0ge1xuICAgICAgICAgIGhpdHM6IGFuYWx5dGljcy5jYWNoZV9oaXRzIHx8IDAsXG4gICAgICAgICAgbWlzc2VzOiBhbmFseXRpY3MuY2FjaGVfbWlzc2VzIHx8IDAsXG4gICAgICAgICAgY29zdFNhdmVkOiBwYXJzZUZsb2F0KGFuYWx5dGljcy5jb3N0X3NhdmVkIHx8ICcwJyksXG4gICAgICAgICAgcmVzcG9uc2VUaW1lU2F2ZWQ6IGFuYWx5dGljcy5yZXNwb25zZV90aW1lX3NhdmVkX21zIHx8IDBcbiAgICAgICAgfTtcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIENhbGN1bGF0ZSBjYWNoZSBoaXQgcmF0ZVxuICAgIGNvbnN0IHRvdGFsUmVxdWVzdHMgPSB0b3RhbEhpdHMgKyB0b3RhbE1pc3NlcztcbiAgICBjb25zdCBjYWNoZUhpdFJhdGUgPSB0b3RhbFJlcXVlc3RzID4gMCA/ICh0b3RhbEhpdHMgLyB0b3RhbFJlcXVlc3RzKSAqIDEwMCA6IDA7XG5cbiAgICAvLyBHZXQgY2FjaGUgZW50cmllcyBieSB0aWVyXG4gICAgY29uc3QgeyBkYXRhOiBjYWNoZUJ5VGllckRhdGEgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnc2VtYW50aWNfY2FjaGUnKVxuICAgICAgLnNlbGVjdCgnY2FjaGVfdGllcicpXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgICAgLmd0ZSgnY3JlYXRlZF9hdCcsIHN0YXJ0RGF0ZS50b0lTT1N0cmluZygpKTtcblxuICAgIGNvbnN0IGNhY2hlQnlUaWVyID0ge307XG4gICAgaWYgKGNhY2hlQnlUaWVyRGF0YSkge1xuICAgICAgY2FjaGVCeVRpZXJEYXRhLmZvckVhY2goZW50cnkgPT4ge1xuICAgICAgICBjb25zdCB0aWVyID0gZW50cnkuY2FjaGVfdGllciB8fCAndW5rbm93bic7XG4gICAgICAgIGNhY2hlQnlUaWVyW3RpZXJdID0gKGNhY2hlQnlUaWVyW3RpZXJdIHx8IDApICsgMTtcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIEdldCBjYWNoZSBlbnRyaWVzIGJ5IHByb3ZpZGVyXG4gICAgY29uc3QgeyBkYXRhOiBjYWNoZUJ5UHJvdmlkZXJEYXRhIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3NlbWFudGljX2NhY2hlJylcbiAgICAgIC5zZWxlY3QoJ3Byb3ZpZGVyX3VzZWQnKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5ndGUoJ2NyZWF0ZWRfYXQnLCBzdGFydERhdGUudG9JU09TdHJpbmcoKSk7XG5cbiAgICBjb25zdCBjYWNoZUJ5UHJvdmlkZXIgPSB7fTtcbiAgICBpZiAoY2FjaGVCeVByb3ZpZGVyRGF0YSkge1xuICAgICAgY2FjaGVCeVByb3ZpZGVyRGF0YS5mb3JFYWNoKGVudHJ5ID0+IHtcbiAgICAgICAgY29uc3QgcHJvdmlkZXIgPSBlbnRyeS5wcm92aWRlcl91c2VkIHx8ICd1bmtub3duJztcbiAgICAgICAgY2FjaGVCeVByb3ZpZGVyW3Byb3ZpZGVyXSA9IChjYWNoZUJ5UHJvdmlkZXJbcHJvdmlkZXJdIHx8IDApICsgMTtcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIENvbnZlcnQgdG8gdGltZSBzZXJpZXMgZm9ybWF0IGZvciBjaGFydHNcbiAgICBjb25zdCBjYWNoZVRpbWVTZXJpZXNEYXRhID0gW107XG4gICAgZm9yIChsZXQgaSA9IGRheXMgLSAxOyBpID49IDA7IGktLSkge1xuICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKCk7XG4gICAgICBkYXRlLnNldERhdGUoZGF0ZS5nZXREYXRlKCkgLSBpKTtcbiAgICAgIGNvbnN0IGRhdGVTdHIgPSBkYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXTtcbiAgICAgIGNvbnN0IGRheURhdGEgPSBkYWlseUNhY2hlRGF0YVtkYXRlU3RyXSB8fCB7IGhpdHM6IDAsIG1pc3NlczogMCwgY29zdFNhdmVkOiAwLCByZXNwb25zZVRpbWVTYXZlZDogMCB9O1xuICAgICAgXG4gICAgICBjYWNoZVRpbWVTZXJpZXNEYXRhLnB1c2goe1xuICAgICAgICBkYXRlOiBkYXRlU3RyLFxuICAgICAgICBoaXRzOiBkYXlEYXRhLmhpdHMsXG4gICAgICAgIG1pc3NlczogZGF5RGF0YS5taXNzZXMsXG4gICAgICAgIGhpdFJhdGU6IChkYXlEYXRhLmhpdHMgKyBkYXlEYXRhLm1pc3NlcykgPiAwID8gKGRheURhdGEuaGl0cyAvIChkYXlEYXRhLmhpdHMgKyBkYXlEYXRhLm1pc3NlcykpICogMTAwIDogMCxcbiAgICAgICAgY29zdFNhdmVkOiBkYXlEYXRhLmNvc3RTYXZlZCxcbiAgICAgICAgcmVzcG9uc2VUaW1lU2F2ZWQ6IGRheURhdGEucmVzcG9uc2VUaW1lU2F2ZWRcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIEdldCB0b3AgY2FjaGVkIG1vZGVsc1xuICAgIGNvbnN0IHsgZGF0YTogY2FjaGVkTW9kZWxzRGF0YSB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdzZW1hbnRpY19jYWNoZScpXG4gICAgICAuc2VsZWN0KCdtb2RlbF91c2VkJylcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAuZ3RlKCdjcmVhdGVkX2F0Jywgc3RhcnREYXRlLnRvSVNPU3RyaW5nKCkpO1xuXG4gICAgY29uc3QgbW9kZWxDYWNoZVVzYWdlID0ge307XG4gICAgaWYgKGNhY2hlZE1vZGVsc0RhdGEpIHtcbiAgICAgIGNhY2hlZE1vZGVsc0RhdGEuZm9yRWFjaChlbnRyeSA9PiB7XG4gICAgICAgIGNvbnN0IG1vZGVsID0gZW50cnkubW9kZWxfdXNlZCB8fCAndW5rbm93bic7XG4gICAgICAgIG1vZGVsQ2FjaGVVc2FnZVttb2RlbF0gPSAobW9kZWxDYWNoZVVzYWdlW21vZGVsXSB8fCAwKSArIDE7XG4gICAgICB9KTtcbiAgICB9XG5cbiAgICBjb25zdCB0b3BDYWNoZWRNb2RlbHMgPSBPYmplY3QuZW50cmllcyhtb2RlbENhY2hlVXNhZ2UpXG4gICAgICAuc29ydCgoWyxhXSwgWyxiXSkgPT4gYiAtIGEpXG4gICAgICAuc2xpY2UoMCwgMTApXG4gICAgICAubWFwKChbbW9kZWwsIGNvdW50XSkgPT4gKHsgbW9kZWwsIGNvdW50IH0pKTtcblxuICAgIC8vIENhbGN1bGF0ZSBjYWNoZSBlZmZpY2llbmN5IG1ldHJpY3NcbiAgICBjb25zdCBhdmVyYWdlUmVzcG9uc2VUaW1lU2F2ZWQgPSB0b3RhbEhpdHMgPiAwID8gdG90YWxSZXNwb25zZVRpbWVTYXZlZCAvIHRvdGFsSGl0cyA6IDA7XG4gICAgY29uc3QgYXZlcmFnZUNvc3RTYXZlZFBlckhpdCA9IHRvdGFsSGl0cyA+IDAgPyB0b3RhbENvc3RTYXZlZCAvIHRvdGFsSGl0cyA6IDA7XG5cbiAgICAvLyBHZXQgY2FjaGUgc3RvcmFnZSB1c2FnZVxuICAgIGNvbnN0IHsgZGF0YTogY2FjaGVTdG9yYWdlRGF0YSB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdzZW1hbnRpY19jYWNoZScpXG4gICAgICAuc2VsZWN0KCdwcm9tcHRfdGV4dCwgcmVzcG9uc2VfZGF0YScpXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgICAgLmd0ZSgnY3JlYXRlZF9hdCcsIHN0YXJ0RGF0ZS50b0lTT1N0cmluZygpKTtcblxuICAgIGxldCB0b3RhbFN0b3JhZ2VCeXRlcyA9IDA7XG4gICAgaWYgKGNhY2hlU3RvcmFnZURhdGEpIHtcbiAgICAgIGNhY2hlU3RvcmFnZURhdGEuZm9yRWFjaChlbnRyeSA9PiB7XG4gICAgICAgIGNvbnN0IHByb21wdFNpemUgPSBuZXcgQmxvYihbZW50cnkucHJvbXB0X3RleHQgfHwgJyddKS5zaXplO1xuICAgICAgICBjb25zdCByZXNwb25zZVNpemUgPSBuZXcgQmxvYihbSlNPTi5zdHJpbmdpZnkoZW50cnkucmVzcG9uc2VfZGF0YSB8fCB7fSldKS5zaXplO1xuICAgICAgICB0b3RhbFN0b3JhZ2VCeXRlcyArPSBwcm9tcHRTaXplICsgcmVzcG9uc2VTaXplO1xuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gQ2FsY3VsYXRlIGNhY2hlIHRyZW5kIChjb21wYXJlIHdpdGggcHJldmlvdXMgcGVyaW9kKVxuICAgIGNvbnN0IHByZXZpb3VzU3RhcnREYXRlID0gbmV3IERhdGUoc3RhcnREYXRlKTtcbiAgICBwcmV2aW91c1N0YXJ0RGF0ZS5zZXREYXRlKHByZXZpb3VzU3RhcnREYXRlLmdldERhdGUoKSAtIGRheXMpO1xuXG4gICAgY29uc3QgeyBkYXRhOiBwcmV2aW91c0NhY2hlRGF0YSB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdzZW1hbnRpY19jYWNoZV9hbmFseXRpY3MnKVxuICAgICAgLnNlbGVjdCgnY2FjaGVfaGl0cywgY2FjaGVfbWlzc2VzJylcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAuZ3RlKCdkYXRlJywgcHJldmlvdXNTdGFydERhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdKVxuICAgICAgLmx0KCdkYXRlJywgc3RhcnREYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSk7XG5cbiAgICBsZXQgcHJldmlvdXNIaXRzID0gMDtcbiAgICBsZXQgcHJldmlvdXNNaXNzZXMgPSAwO1xuICAgIGlmIChwcmV2aW91c0NhY2hlRGF0YSkge1xuICAgICAgcHJldmlvdXNDYWNoZURhdGEuZm9yRWFjaChhbmFseXRpY3MgPT4ge1xuICAgICAgICBwcmV2aW91c0hpdHMgKz0gYW5hbHl0aWNzLmNhY2hlX2hpdHMgfHwgMDtcbiAgICAgICAgcHJldmlvdXNNaXNzZXMgKz0gYW5hbHl0aWNzLmNhY2hlX21pc3NlcyB8fCAwO1xuICAgICAgfSk7XG4gICAgfVxuXG4gICAgY29uc3QgcHJldmlvdXNUb3RhbFJlcXVlc3RzID0gcHJldmlvdXNIaXRzICsgcHJldmlvdXNNaXNzZXM7XG4gICAgY29uc3QgcHJldmlvdXNIaXRSYXRlID0gcHJldmlvdXNUb3RhbFJlcXVlc3RzID4gMCA/IChwcmV2aW91c0hpdHMgLyBwcmV2aW91c1RvdGFsUmVxdWVzdHMpICogMTAwIDogMDtcbiAgICBjb25zdCBoaXRSYXRlVHJlbmQgPSBjYWNoZUhpdFJhdGUgLSBwcmV2aW91c0hpdFJhdGU7XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIGRhdGE6IHtcbiAgICAgICAgLy8gU3VtbWFyeSBtZXRyaWNzXG4gICAgICAgIHRvdGFsQ2FjaGVFbnRyaWVzOiB0b3RhbENhY2hlRW50cmllcyB8fCAwLFxuICAgICAgICB0b3RhbEhpdHMsXG4gICAgICAgIHRvdGFsTWlzc2VzLFxuICAgICAgICBjYWNoZUhpdFJhdGUsXG4gICAgICAgIGhpdFJhdGVUcmVuZCxcbiAgICAgICAgdG90YWxDb3N0U2F2ZWQsXG4gICAgICAgIHRvdGFsUmVzcG9uc2VUaW1lU2F2ZWQsXG4gICAgICAgIGF2ZXJhZ2VSZXNwb25zZVRpbWVTYXZlZCxcbiAgICAgICAgYXZlcmFnZUNvc3RTYXZlZFBlckhpdCxcbiAgICAgICAgdG90YWxTdG9yYWdlQnl0ZXMsXG4gICAgICAgIFxuICAgICAgICAvLyBCcmVha2Rvd25zXG4gICAgICAgIGNhY2hlQnlUaWVyOiBPYmplY3QuZW50cmllcyhjYWNoZUJ5VGllcikubWFwKChbdGllciwgY291bnRdKSA9PiAoeyB0aWVyLCBjb3VudCB9KSksXG4gICAgICAgIGNhY2hlQnlQcm92aWRlcjogT2JqZWN0LmVudHJpZXMoY2FjaGVCeVByb3ZpZGVyKS5tYXAoKFtwcm92aWRlciwgY291bnRdKSA9PiAoeyBwcm92aWRlciwgY291bnQgfSkpLFxuICAgICAgICB0b3BDYWNoZWRNb2RlbHMsXG4gICAgICAgIFxuICAgICAgICAvLyBUaW1lIHNlcmllcyBkYXRhXG4gICAgICAgIGNhY2hlVGltZVNlcmllc0RhdGEsXG4gICAgICAgIFxuICAgICAgICAvLyBQZXJpb2QgaW5mb1xuICAgICAgICBwZXJpb2Q6IGAke2RheXN9IGRheXNgLFxuICAgICAgICBzdGFydERhdGU6IHN0YXJ0RGF0ZS50b0lTT1N0cmluZygpLFxuICAgICAgICBlbmREYXRlOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH1cbiAgICB9KTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIGNhY2hlIGFuYWx5dGljczonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIGVycm9yOiAnRmFpbGVkIHRvIGZldGNoIGNhY2hlIGFuYWx5dGljcycsXG4gICAgICBkZXRhaWxzOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJ1xuICAgIH0sIHsgc3RhdHVzOiA1MDAgfSk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJjcmVhdGVTdXBhYmFzZVNlcnZlckNsaWVudEZyb21SZXF1ZXN0IiwiR0VUIiwicmVxdWVzdCIsInN1cGFiYXNlIiwiZGF0YSIsInVzZXIiLCJlcnJvciIsImF1dGhFcnJvciIsImF1dGgiLCJnZXRVc2VyIiwianNvbiIsInN0YXR1cyIsInNlYXJjaFBhcmFtcyIsIlVSTCIsInVybCIsImRheXMiLCJwYXJzZUludCIsImdldCIsInN0YXJ0RGF0ZSIsIkRhdGUiLCJzZXREYXRlIiwiZ2V0RGF0ZSIsImNhY2hlQW5hbHl0aWNzRGF0YSIsImZyb20iLCJzZWxlY3QiLCJlcSIsImlkIiwiZ3RlIiwidG9JU09TdHJpbmciLCJzcGxpdCIsIm9yZGVyIiwiYXNjZW5kaW5nIiwiY2FjaGVFbnRyaWVzRGF0YSIsImNvdW50IiwidG90YWxDYWNoZUVudHJpZXMiLCJ0b3RhbEhpdHMiLCJ0b3RhbE1pc3NlcyIsInRvdGFsQ29zdFNhdmVkIiwidG90YWxSZXNwb25zZVRpbWVTYXZlZCIsImRhaWx5Q2FjaGVEYXRhIiwibGVuZ3RoIiwiZm9yRWFjaCIsImFuYWx5dGljcyIsImNhY2hlX2hpdHMiLCJjYWNoZV9taXNzZXMiLCJwYXJzZUZsb2F0IiwiY29zdF9zYXZlZCIsInJlc3BvbnNlX3RpbWVfc2F2ZWRfbXMiLCJkYXRlIiwiaGl0cyIsIm1pc3NlcyIsImNvc3RTYXZlZCIsInJlc3BvbnNlVGltZVNhdmVkIiwidG90YWxSZXF1ZXN0cyIsImNhY2hlSGl0UmF0ZSIsImNhY2hlQnlUaWVyRGF0YSIsImNhY2hlQnlUaWVyIiwiZW50cnkiLCJ0aWVyIiwiY2FjaGVfdGllciIsImNhY2hlQnlQcm92aWRlckRhdGEiLCJjYWNoZUJ5UHJvdmlkZXIiLCJwcm92aWRlciIsInByb3ZpZGVyX3VzZWQiLCJjYWNoZVRpbWVTZXJpZXNEYXRhIiwiaSIsImRhdGVTdHIiLCJkYXlEYXRhIiwicHVzaCIsImhpdFJhdGUiLCJjYWNoZWRNb2RlbHNEYXRhIiwibW9kZWxDYWNoZVVzYWdlIiwibW9kZWwiLCJtb2RlbF91c2VkIiwidG9wQ2FjaGVkTW9kZWxzIiwiT2JqZWN0IiwiZW50cmllcyIsInNvcnQiLCJhIiwiYiIsInNsaWNlIiwibWFwIiwiYXZlcmFnZVJlc3BvbnNlVGltZVNhdmVkIiwiYXZlcmFnZUNvc3RTYXZlZFBlckhpdCIsImNhY2hlU3RvcmFnZURhdGEiLCJ0b3RhbFN0b3JhZ2VCeXRlcyIsInByb21wdFNpemUiLCJCbG9iIiwicHJvbXB0X3RleHQiLCJzaXplIiwicmVzcG9uc2VTaXplIiwiSlNPTiIsInN0cmluZ2lmeSIsInJlc3BvbnNlX2RhdGEiLCJwcmV2aW91c1N0YXJ0RGF0ZSIsInByZXZpb3VzQ2FjaGVEYXRhIiwibHQiLCJwcmV2aW91c0hpdHMiLCJwcmV2aW91c01pc3NlcyIsInByZXZpb3VzVG90YWxSZXF1ZXN0cyIsInByZXZpb3VzSGl0UmF0ZSIsImhpdFJhdGVUcmVuZCIsInN1Y2Nlc3MiLCJwZXJpb2QiLCJlbmREYXRlIiwiY29uc29sZSIsImRldGFpbHMiLCJFcnJvciIsIm1lc3NhZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analytics/cache/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fcache%2Froute&page=%2Fapi%2Fanalytics%2Fcache%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fcache%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();